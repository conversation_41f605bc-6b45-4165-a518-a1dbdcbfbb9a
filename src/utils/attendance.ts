import { isNumber } from 'lodash'

import { AttendanceShift } from '../types/attendance'

type ShiftsObject = { [key: string]: AttendanceShift }

export type OverlappingShifts = {
  [key: string]: { isStartOverlapping: boolean; isEndOverlapping: boolean }
}

type ConvertedShift = {
  key: string
} & Pick<AttendanceShift, 'start' | 'end'>

const MINUTES_IN_DAY = 1440

export const checkShiftOverlap = ({
  nextDayShifts,
  employeeShifts,
  previousDayShifts
}: {
  nextDayShifts: ShiftsObject
  employeeShifts: ShiftsObject
  previousDayShifts: ShiftsObject
}): OverlappingShifts => {
  const previousDayLastShift = getLastDayShift(previousDayShifts)
  const nextDayFirstShift = getFirstDayShift(nextDayShifts)

  const currentDayShifts = Object.entries(employeeShifts).map(convertShiftEntry)

  // Only check if we have shifts that could overlap
  if (
    !currentDayShifts.length ||
    (!previousDayLastShift &&
      !nextDayFirstShift &&
      currentDayShifts.length === 1)
  ) {
    return {}
  }

  const overlappingShifts: OverlappingShifts = {}

  currentDayShifts.forEach(shift => {
    overlappingShifts[shift.key] = {
      isStartOverlapping: false,
      isEndOverlapping: false
    }
  })

  // Check overlaps for all shifts in current day
  currentDayShifts.forEach(currentShift => {
    const adjustedCurrentEnd = getAdjustedEndTime(currentShift)

    // Check overlap with previous day
    if (previousDayLastShift) {
      if (
        isOverlappingWithPreviousDayShift(currentShift, previousDayLastShift)
      ) {
        overlappingShifts[currentShift.key].isStartOverlapping = true
      }
    }

    // Check overlap with other current day shifts
    currentDayShifts.forEach(otherShift => {
      if (currentShift.key !== otherShift.key) {
        if (
          isOverlappingWithCurrentDayShifts(
            currentShift,
            otherShift,
            adjustedCurrentEnd
          )
        ) {
          overlappingShifts[currentShift.key].isEndOverlapping = true
          overlappingShifts[otherShift.key].isStartOverlapping = true
        }
      }
    })

    // Check overlap with next day
    if (nextDayFirstShift) {
      if (isOverlappingWithNextDayShift(currentShift, nextDayFirstShift)) {
        overlappingShifts[currentShift.key].isEndOverlapping = true
      }
    }
  })

  return overlappingShifts
}

const isOverlappingWithPreviousDayShift = (
  current: ConvertedShift,
  previous: ConvertedShift
): boolean => {
  // If previous shift has no end time (no clock-out),
  // it overlaps with current shift only if current starts before 5am
  if (!isNumber(previous.end)) {
    return current.start! < 300
  }

  if (
    previous.end === 0 || // if previous shift ends at 00:00
    previous.end! > previous.start! || // if previous shift ends before midnight
    previous.end === current.start || // if previous shift.end is the same as current shift.start
    previous.start === previous.end // MUST REMOVE TEMP FIX: when we'll add a duration minimum for a shift
  )
    return false

  return current.start! < previous.end!
}

const isOverlappingWithCurrentDayShifts = (
  current: ConvertedShift,
  other: ConvertedShift,
  adjustedCurrentEnd: number
): boolean => {
  const adjustedOtherStart =
    other.start! < current.start! ? other.start! + MINUTES_IN_DAY : other.start!

  const adjustedOtherEnd = getAdjustedEndTime(other)

  // Shifts overlap if:
  // 1. Other shift starts before current shift ends AND
  // 2. Other shift ends after current shift starts AND
  // 3. They don't just touch (one ends exactly when other starts)
  return (
    adjustedOtherStart < adjustedCurrentEnd &&
    adjustedOtherEnd > current.start! &&
    !(
      adjustedOtherStart === adjustedCurrentEnd ||
      adjustedOtherEnd === current.start!
    )
  )
}

const isOverlappingWithNextDayShift = (
  current: ConvertedShift,
  next: ConvertedShift
): boolean => {
  const adjustedCurrentEnd = getAdjustedEndTime(current)
  const adjustedNextStart = next.start! + MINUTES_IN_DAY
  // Don't consider touching shifts as overlapping
  return (
    adjustedNextStart < adjustedCurrentEnd &&
    adjustedNextStart !== adjustedCurrentEnd
  )
}

const convertShiftEntry = (
  shiftEntry: [string, AttendanceShift]
): ConvertedShift => {
  const [key, shift] = shiftEntry
  return {
    key,
    start: shift.start,
    end: shift.end
  }
}

const getLastDayShift = (shifts: ShiftsObject) => {
  if (!shifts) return null
  const lastShift = Object.entries(shifts)
    .sort((a, b) => getAdjustedEndTime(a[1]) - getAdjustedEndTime(b[1]))
    .pop()

  return lastShift ? convertShiftEntry(lastShift) : null
}

const getFirstDayShift = (shifts: ShiftsObject) => {
  if (!shifts) return null
  const firstShift = Object.entries(shifts)
    .sort((a, b) => a[1].start! - b[1].start!)
    .shift()

  return firstShift ? convertShiftEntry(firstShift) : null
}

const getAdjustedEndTime = (
  shift: AttendanceShift | ConvertedShift
): number => {
  if (!isNumber(shift.end)) return 300
  return shift.end < shift.start! ? shift.end + MINUTES_IN_DAY : shift.end
}
