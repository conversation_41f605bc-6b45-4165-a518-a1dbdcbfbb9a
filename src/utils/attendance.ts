import { isNumber } from 'lodash'

import { AttendanceShift } from '../types/attendance'

type ShiftsObject = { [key: string]: AttendanceShift }

export type OverlappingShifts = {
  [key: string]: { isStartOverlapping: boolean; isEndOverlapping: boolean }
}

type ConvertedShift = {
  key: string
} & Pick<AttendanceShift, 'start' | 'end'>

const MINUTES_IN_DAY = 1440

export const checkShiftOverlap = ({
  nextDayShifts,
  employeeShifts,
  previousDayShifts
}: {
  nextDayShifts: ShiftsObject
  employeeShifts: ShiftsObject
  previousDayShifts: ShiftsObject
}): OverlappingShifts => {
  const previousDayLastShift = getLastDayShift(previousDayShifts)
  const nextDayFirstShift = getFirstDayShift(nextDayShifts)

  const currentDayShifts = Object.entries(employeeShifts).map(convertShiftEntry)

  // Only check if we have shifts that could overlap
  if (
    !currentDayShifts.length ||
    (!previousDayLastShift &&
      !nextDayFirstShift &&
      currentDayShifts.length === 1)
  ) {
    return {}
  }

  const overlappingShifts: OverlappingShifts = {}

  currentDayShifts.forEach(shift => {
    overlappingShifts[shift.key] = {
      isStartOverlapping: false,
      isEndOverlapping: false
    }
  })

  // Check overlaps for all shifts in current day
  currentDayShifts.forEach(currentShift => {
    const adjustedCurrentEnd = getAdjustedEndTime(currentShift)

    // Check overlap with previous day
    if (previousDayLastShift) {
      if (
        isOverlappingWithPreviousDayShift(currentShift, previousDayLastShift)
      ) {
        overlappingShifts[currentShift.key].isStartOverlapping = true
      }
    }

    // Check overlap with other current day shifts
    currentDayShifts.forEach(otherShift => {
      if (currentShift.key !== otherShift.key) {
        if (
          isOverlappingWithCurrentDayShifts(
            currentShift,
            otherShift,
            adjustedCurrentEnd
          )
        ) {
          overlappingShifts[currentShift.key].isEndOverlapping = true
          overlappingShifts[otherShift.key].isStartOverlapping = true
        }
      }
    })

    // Check overlap with next day
    if (nextDayFirstShift) {
      if (isOverlappingWithNextDayShift(currentShift, nextDayFirstShift)) {
        overlappingShifts[currentShift.key].isEndOverlapping = true
      }
    }
  })

  // Debug: Log final overlap results
  const hasAnyOverlaps = Object.values(overlappingShifts).some(
    overlap => overlap.isStartOverlapping || overlap.isEndOverlapping
  )

  if (hasAnyOverlaps) {
    console.log('🚨 Final Overlap Results:', {
      overlappingShifts: Object.entries(overlappingShifts)
        .filter(
          ([_, overlap]) =>
            overlap.isStartOverlapping || overlap.isEndOverlapping
        )
        .map(([shiftKey, overlap]) => ({
          shiftKey,
          isStartOverlapping: overlap.isStartOverlapping,
          isEndOverlapping: overlap.isEndOverlapping
        })),
      allShifts: Object.keys(employeeShifts).map(key => {
        const shift = employeeShifts[key]
        return `${key}: ${formatMinutes(shift.start || 0)} - ${formatMinutes(shift.end || 0)}`
      })
    })
  }

  return overlappingShifts
}

const isOverlappingWithPreviousDayShift = (
  current: ConvertedShift,
  previous: ConvertedShift
): boolean => {
  // If previous shift has no end time (no clock-out),
  // it overlaps with current shift only if current starts before 5am
  if (!isNumber(previous.end)) {
    const isOverlapping = current.start! < 300
    console.log('🔍 Previous Day Overlap Check (no end time):', {
      currentShift: `${current.key}: ${formatMinutes(current.start!)} - ?`,
      previousShift: `${previous.key}: ${formatMinutes(previous.start!)} - NO_END`,
      condition: `${formatMinutes(current.start!)} < 05:00 = ${isOverlapping}`,
      result: isOverlapping ? '❌ OVERLAPPING' : '✅ NOT OVERLAPPING'
    })
    return isOverlapping
  }

  const condition1 = previous.end === 0 // ends at 00:00
  const condition2 = previous.end! > previous.start! // ends before midnight (normal shift)
  const condition3 = previous.end === current.start // touching shifts
  const condition4 = previous.start === previous.end // zero duration shift

  const shouldSkip = condition1 || condition2 || condition3 || condition4

  if (!shouldSkip) {
    const isOverlapping = current.start! < previous.end!
    console.log('🔍 Previous Day Overlap Check (overnight shift):', {
      currentShift: `${current.key}: ${formatMinutes(current.start!)} - ?`,
      previousShift: `${previous.key}: ${formatMinutes(previous.start!)} - ${formatMinutes(previous.end!)}`,
      condition1: `previous.end === 0: ${condition1}`,
      condition2: `previous.end > previous.start (normal shift): ${condition2}`,
      condition3: `previous.end === current.start (touching): ${condition3}`,
      condition4: `previous.start === previous.end (zero duration): ${condition4}`,
      shouldSkip,
      overlapCheck: shouldSkip
        ? 'SKIPPED'
        : `${formatMinutes(current.start!)} < ${formatMinutes(previous.end!)} = ${isOverlapping}`,
      result: shouldSkip
        ? '✅ NOT OVERLAPPING (skipped)'
        : isOverlapping
          ? '❌ OVERLAPPING'
          : '✅ NOT OVERLAPPING'
    })
    return isOverlapping
  }

  return false
}

const isOverlappingWithCurrentDayShifts = (
  current: ConvertedShift,
  other: ConvertedShift,
  adjustedCurrentEnd: number
): boolean => {
  const adjustedOtherStart =
    other.start! < current.start! ? other.start! + MINUTES_IN_DAY : other.start!

  const adjustedOtherEnd = getAdjustedEndTime(other)

  const condition1 = adjustedOtherStart < adjustedCurrentEnd
  const condition2 = adjustedOtherEnd > current.start!
  const touchingShifts =
    adjustedOtherStart === adjustedCurrentEnd ||
    adjustedOtherEnd === current.start!

  const isOverlapping = condition1 && condition2 && !touchingShifts

  // Debug logging for overlap detection
  if (condition1 && condition2) {
    console.log('🔍 Current Day Overlap Check:', {
      currentShift: `${current.key}: ${formatMinutes(current.start!)} - ${formatMinutes(adjustedCurrentEnd)}`,
      otherShift: `${other.key}: ${formatMinutes(adjustedOtherStart)} - ${formatMinutes(adjustedOtherEnd)}`,
      condition1: `${formatMinutes(adjustedOtherStart)} < ${formatMinutes(adjustedCurrentEnd)} = ${condition1}`,
      condition2: `${formatMinutes(adjustedOtherEnd)} > ${formatMinutes(current.start!)} = ${condition2}`,
      touchingShifts: `${formatMinutes(adjustedOtherStart)} === ${formatMinutes(adjustedCurrentEnd)} || ${formatMinutes(adjustedOtherEnd)} === ${formatMinutes(current.start!)} = ${touchingShifts}`,
      result: isOverlapping ? '❌ OVERLAPPING' : '✅ NOT OVERLAPPING (touching)'
    })
  }

  // Shifts overlap if:
  // 1. Other shift starts before current shift ends AND
  // 2. Other shift ends after current shift starts AND
  // 3. They don't just touch (one ends exactly when other starts)
  return isOverlapping
}

// Helper function to format minutes to HH:MM
const formatMinutes = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

const isOverlappingWithNextDayShift = (
  current: ConvertedShift,
  next: ConvertedShift
): boolean => {
  const adjustedCurrentEnd = getAdjustedEndTime(current)
  const adjustedNextStart = next.start! + MINUTES_IN_DAY

  const condition1 = adjustedNextStart < adjustedCurrentEnd
  const touchingShifts = adjustedNextStart === adjustedCurrentEnd
  const isOverlapping = condition1 && !touchingShifts

  if (condition1) {
    console.log('🔍 Next Day Overlap Check:', {
      currentShift: `${current.key}: ${formatMinutes(current.start!)} - ${formatMinutes(adjustedCurrentEnd)}`,
      nextShift: `${next.key}: ${formatMinutes(adjustedNextStart)} - ?`,
      condition1: `${formatMinutes(adjustedNextStart)} < ${formatMinutes(adjustedCurrentEnd)} = ${condition1}`,
      touchingShifts: `${formatMinutes(adjustedNextStart)} === ${formatMinutes(adjustedCurrentEnd)} = ${touchingShifts}`,
      result: isOverlapping ? '❌ OVERLAPPING' : '✅ NOT OVERLAPPING (touching)'
    })
  }

  // Don't consider touching shifts as overlapping
  return isOverlapping
}

const convertShiftEntry = (
  shiftEntry: [string, AttendanceShift]
): ConvertedShift => {
  const [key, shift] = shiftEntry
  return {
    key,
    start: shift.start,
    end: shift.end
  }
}

const getLastDayShift = (shifts: ShiftsObject) => {
  if (!shifts) return null
  const lastShift = Object.entries(shifts)
    .sort((a, b) => getAdjustedEndTime(a[1]) - getAdjustedEndTime(b[1]))
    .pop()

  return lastShift ? convertShiftEntry(lastShift) : null
}

const getFirstDayShift = (shifts: ShiftsObject) => {
  if (!shifts) return null
  const firstShift = Object.entries(shifts)
    .sort((a, b) => a[1].start! - b[1].start!)
    .shift()

  return firstShift ? convertShiftEntry(firstShift) : null
}

const getAdjustedEndTime = (
  shift: AttendanceShift | ConvertedShift
): number => {
  if (!isNumber(shift.end)) return 300
  return shift.end < shift.start! ? shift.end + MINUTES_IN_DAY : shift.end
}
