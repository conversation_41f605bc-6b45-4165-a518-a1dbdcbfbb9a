/**
 * Quick Test Script for ShiftPopover Conflict Analysis
 * 
 * This script provides a simple way to test the implementation
 * Run this from the browser console after the app loads
 */

import { saveTestAttendanceData, clearTestAttendanceData } from './attendanceTestDataGenerator'

// Make it available globally for easy console access
declare global {
  interface Window {
    quickTest: {
      createTestData: (companyId: string) => Promise<void>
      clearTestData: (companyId: string) => Promise<void>
      help: () => void
    }
  }
}

const createTestData = async (companyId: string) => {
  try {
    console.log('🚀 Creating test data for ShiftPopover conflict analysis...')
    await saveTestAttendanceData(companyId)
    console.log('✅ Test data created successfully!')
    console.log('📍 Navigate to Payroll → Hours to test the implementation')
    console.log('🔍 Look for colored shift tabs and click on employee shifts to test popover')
  } catch (error) {
    console.error('❌ Failed to create test data:', error)
  }
}

const clearTestData = async (companyId: string) => {
  try {
    console.log('🧹 Clearing test data...')
    await clearTestAttendanceData(companyId)
    console.log('✅ Test data cleared successfully!')
  } catch (error) {
    console.error('❌ Failed to clear test data:', error)
  }
}

const help = () => {
  console.log(`
🧪 ShiftPopover Conflict Analysis Test Helper

Available commands:
  window.quickTest.createTestData('your-company-id')  - Create test data
  window.quickTest.clearTestData('your-company-id')   - Clear test data
  window.quickTest.help()                             - Show this help

Test Scenarios Created:
  🔴 Red Conflicts (Must resolve immediately)
    - Employee 1: Overlapping shifts
    - Employee 1 (tomorrow): Multiple conflicts
  
  🟠 Orange Conflicts (Require approval/modification)
    - Employee 2: Early arrival
    - Employee 4: Missing clock out
    - Employee 5: Short shift
    - Employee 2 (tomorrow): Long shift
    - Employee 5 (tomorrow): Late departure
  
  ⚫ Grey Conflicts (Missing required fields)
    - Employee 3: Missing role selection
  
  🟢 Green Status (Perfect shifts)
    - Employee 3 (tomorrow): Properly configured

What to Test:
  1. Navigate to Payroll → Hours
  2. Look for colored shift tabs with warning icons
  3. Click on employee shifts to open popover
  4. Verify conflict indicators and button behaviors
  5. Test tooltip messages and button states

Expected Results:
  ✅ Red conflicts block all actions
  ✅ Orange conflicts allow approval with warnings  
  ✅ Grey conflicts block saving until fields completed
  ✅ Green status allows normal operations
  ✅ Visual indicators clearly show conflict types
  ✅ Tooltips provide specific guidance
`)
}

// Attach to window for console access
if (typeof window !== 'undefined') {
  window.quickTest = {
    createTestData,
    clearTestData,
    help
  }
  
  // Show help on load
  console.log('🧪 ShiftPopover Test Helper loaded! Run window.quickTest.help() for instructions')
}

export { createTestData, clearTestData, help }
