import dayjs from 'dayjs'

import { analyzeShiftConflicts } from './shiftConflictUtils'

import { checkShiftOverlap } from 'utils/attendance'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company } from 'types/company'

// Constants for validation thresholds
const MINIMUM_SHIFT_DURATION_MINUTES = 30 // Shifts shorter than 30 minutes are considered too short
// Removed MINIMUM_WAGE_HOURS - no longer enforcing 3-hour minimum
const BREAK_TOLERANCE_MINUTES = 15 // Tolerance for break duration compliance

export type ShiftStatus =
  | 'current' // Currently punched in (blue)
  | 'approved' // Shift approved by Pi<PERSON><PERSON> or manager (white)
  | 'other-role' // Shift belongs to another role (grey)
  | 'problematic' // Problematic shift needing manager resolution (orange)
  | 'overlapping' // Overlapping shift requiring immediate correction (red)

export type ShiftAnalysis = {
  status: ShiftStatus
  priority: number // Higher number = higher priority for display
  issues: string[] // List of specific issues found
  conflictField?: 'start' | 'end' // Which time field has the conflict (for overlaps)
  breakIssues?: {
    unpaidBreakIssues: string[]
    paidBreakIssues: string[]
  }
}

/**
 * Analyzes a shift for all user story scenarios and returns comprehensive status
 */
export const analyzeShiftStatus = (
  shift: AttendanceShift,
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts,
  currentCompany: Company,
  isToday: boolean = false
): ShiftAnalysis => {
  const issues: string[] = []
  const breakIssues = {
    unpaidBreakIssues: [] as string[],
    paidBreakIssues: [] as string[]
  }

  // 1. Current shift: Employee currently punched in (BLUE)
  const isCurrentlyWorking = shift.start && !shift.end
  if (isCurrentlyWorking) {
    return {
      status: 'current',
      priority: 10,
      issues: ['Employee is currently punched in'],
      breakIssues
    }
  }

  // Check for overlapping shifts first (RED - highest priority after current)
  const overlapAnalysis = analyzeShiftOverlaps(
    shift,
    employeeId,
    date,
    attendanceData
  )
  if (overlapAnalysis.hasOverlap) {
    return {
      status: 'overlapping',
      priority: 9,
      issues: [...issues, ...overlapAnalysis.overlapIssues],
      conflictField: overlapAnalysis.conflictField,
      breakIssues
    }
  }

  // Check if this shift is problematic (ORANGE)
  const problematicAnalysis = analyzeProblematicShift(
    shift,
    currentCompany,
    isToday
  )
  if (problematicAnalysis.isProblematic) {
    return {
      status: 'problematic',
      priority: 8,
      issues: [...issues, ...problematicAnalysis.issues],
      breakIssues
    }
  }

  // Check if shift belongs to another role (GREY)
  // A shift belongs to another role if it has a positionId that's different from
  // the employee's primary role or if it's marked as a different role shift
  const isDifferentRole =
    shift.positionId &&
    currentCompany.jobs &&
    currentCompany.jobs[shift.positionId] &&
    shift.positionId !== employeeId // This is a simplified check - in reality you'd compare with employee's primary role

  if (isDifferentRole) {
    return {
      status: 'other-role',
      priority: 2,
      issues: [
        ...issues,
        `Shift for different role: ${(shift.positionId && currentCompany.jobs?.[shift.positionId]?.name) || 'Unknown'}`
      ],
      breakIssues
    }
  }

  // Default: Approved shift (WHITE)
  return {
    status: 'approved',
    priority: 1,
    issues: shift.isConfirmed ? [] : ['Shift pending approval'],
    breakIssues
  }
}

/**
 * Analyzes break compliance for both paid and unpaid breaks
 */
const analyzeBreakCompliance = (
  shift: AttendanceShift,
  currentCompany: Company,
  breakIssues: { unpaidBreakIssues: string[]; paidBreakIssues: string[] }
) => {
  if (!shift.breaks || !currentCompany.breaks) return

  const actualBreaks = Object.values(shift.breaks)
  const companyBreaks = currentCompany.breaks

  // Calculate total shift duration in hours
  const shiftDurationHours =
    shift.start && shift.end ? (shift.end - shift.start) / 60 : 0

  // Check each company break policy
  Object.entries(companyBreaks).forEach(([breakId, breakPolicy]) => {
    // Check if employee is entitled to this break based on shift duration
    if (shiftDurationHours >= breakPolicy.hours) {
      const expectedBreakDuration = breakPolicy.length * 60 // Convert to minutes

      // Find actual break taken that matches this policy
      const matchingBreak = findMatchingBreak(
        actualBreaks,
        expectedBreakDuration
      )

      if (!matchingBreak) {
        // Determine if break is paid or unpaid based on company settings
        const isUnpaidBreak = shouldBreakBeUnpaid(breakPolicy, currentCompany)
        const breakType = isUnpaidBreak ? 'unpaid' : 'paid'
        const issueArray = isUnpaidBreak
          ? breakIssues.unpaidBreakIssues
          : breakIssues.paidBreakIssues

        issueArray.push(
          `Missing ${breakPolicy.length}-minute ${breakType} break for ${breakPolicy.hours}+ hour shift`
        )
      } else {
        validateBreakDuration(
          matchingBreak,
          expectedBreakDuration,
          breakPolicy,
          currentCompany,
          breakIssues
        )
      }
    }
  })

  // Check for unauthorized breaks (breaks taken that don't match any policy)
  checkUnauthorizedBreaks(
    actualBreaks,
    companyBreaks,
    shiftDurationHours,
    breakIssues
  )
}

/**
 * Finds a break that matches the expected duration within tolerance
 */
const findMatchingBreak = (actualBreaks: any[], expectedDuration: number) => {
  return actualBreaks.find(breakItem => {
    if (!breakItem.start || !breakItem.end) return false
    const actualDuration = breakItem.end - breakItem.start
    return (
      Math.abs(actualDuration - expectedDuration) <= BREAK_TOLERANCE_MINUTES
    )
  })
}

/**
 * Determines if a break should be unpaid based on company policy
 */
const shouldBreakBeUnpaid = (
  breakPolicy: any,
  currentCompany: Company
): boolean => {
  // Default logic: breaks longer than 30 minutes are typically unpaid
  // This can be enhanced based on company-specific break policies
  return breakPolicy.length >= 30 || currentCompany.excludeBreaks === true
}

/**
 * Validates the duration of a taken break against policy
 */
const validateBreakDuration = (
  actualBreak: any,
  expectedDuration: number,
  breakPolicy: any,
  currentCompany: Company,
  breakIssues: { unpaidBreakIssues: string[]; paidBreakIssues: string[] }
) => {
  const actualDuration = actualBreak.end - actualBreak.start
  const durationDiff = Math.abs(actualDuration - expectedDuration)

  if (durationDiff > BREAK_TOLERANCE_MINUTES) {
    const isUnpaidBreak = shouldBreakBeUnpaid(breakPolicy, currentCompany)
    const breakType = isUnpaidBreak ? 'unpaid' : 'paid'
    const issueArray = isUnpaidBreak
      ? breakIssues.unpaidBreakIssues
      : breakIssues.paidBreakIssues

    const status = actualDuration > expectedDuration ? 'longer' : 'shorter'
    const actualMinutes = Math.round(actualDuration)
    const expectedMinutes = Math.round(expectedDuration)

    issueArray.push(
      `${breakType} break duration ${status} than expected (${actualMinutes} vs ${expectedMinutes} minutes)`
    )
  }
}

/**
 * Checks for breaks taken that don't match any company policy
 */
const checkUnauthorizedBreaks = (
  actualBreaks: any[],
  companyBreaks: any,
  shiftDurationHours: number,
  breakIssues: { unpaidBreakIssues: string[]; paidBreakIssues: string[] }
) => {
  const authorizedBreakDurations = Object.values(companyBreaks).map(
    (policy: any) => policy.length * 60
  )

  actualBreaks.forEach(breakItem => {
    if (!breakItem.start || !breakItem.end) return

    const actualDuration = breakItem.end - breakItem.start
    const isAuthorized = authorizedBreakDurations.some(
      duration => Math.abs(actualDuration - duration) <= BREAK_TOLERANCE_MINUTES
    )

    if (!isAuthorized && actualDuration > 5) {
      // Ignore very short breaks (< 5 minutes)
      const minutes = Math.round(actualDuration)
      breakIssues.unpaidBreakIssues.push(
        `Unauthorized ${minutes}-minute break taken`
      )
    }
  })
}

/**
 * Gets the appropriate color for a shift status
 */
export const getShiftStatusColor = (status: ShiftStatus): string => {
  switch (status) {
    case 'current':
      return 'blue' // Currently working
    case 'approved':
      return 'white' // Shift approved by Pivot or manager
    case 'other-role':
      return 'grey' // Shift belongs to another role
    case 'problematic':
      return 'orange' // Problematic shift, needs manager resolution
    case 'overlapping':
      return 'red' // Overlapping shift, must be corrected immediately
    default:
      return 'white' // Default to approved
  }
}

/**
 * Analyzes if a shift is problematic and needs manager resolution
 */
const analyzeProblematicShift = (
  shift: AttendanceShift,
  currentCompany: Company,
  isToday: boolean
): { isProblematic: boolean; issues: string[] } => {
  const issues: string[] = []
  let isProblematic = false

  // Missing end time for past shifts
  if (shift.start && !shift.end && !isToday) {
    isProblematic = true
    issues.push('Employee forgot to punch out')
  }

  // Shift duration issues
  const durationAnalysis = validateShiftDuration(shift)
  if (durationAnalysis.hasIssues) {
    isProblematic = true
    issues.push(...durationAnalysis.issues)
  }

  // Break compliance issues
  const breakIssues = {
    unpaidBreakIssues: [] as string[],
    paidBreakIssues: [] as string[]
  }
  analyzeBreakCompliance(shift, currentCompany, breakIssues)

  if (
    breakIssues.unpaidBreakIssues.length > 0 ||
    breakIssues.paidBreakIssues.length > 0
  ) {
    isProblematic = true
    issues.push(
      ...breakIssues.unpaidBreakIssues,
      ...breakIssues.paidBreakIssues
    )
  }

  // Conflicting start/end times
  if (shift.start && shift.end) {
    const shiftStartRounded = Math.round(shift.start / 5) * 5
    const shiftEndRounded = Math.round(shift.end / 5) * 5

    const conflictAnalysis = analyzeShiftConflicts(
      shift,
      shiftStartRounded,
      shiftEndRounded,
      null,
      isToday,
      false
    )

    if (conflictAnalysis.isClockInDifferent) {
      isProblematic = true
      issues.push('Employee arrived early - confirm authorization')
    }

    if (conflictAnalysis.isClockOutDifferent) {
      isProblematic = true
      issues.push('Employee left later than scheduled')
    }
  }

  // Unconfirmed shifts
  if (!shift.isConfirmed) {
    isProblematic = true
    issues.push('Shift requires confirmation')
  }

  return { isProblematic, issues }
}

/**
 * Validates shift duration and identifies issues
 */
const validateShiftDuration = (
  shift: AttendanceShift
): {
  hasIssues: boolean
  status: 'minimum-wage' | 'too-short' | 'normal'
  issues: string[]
} => {
  const issues: string[] = []

  if (!shift.start || !shift.end) {
    return { hasIssues: false, status: 'normal', issues }
  }

  const shiftDuration = shift.end - shift.start
  const shiftHours = shiftDuration / 60

  // Check for minimum wage compliance (highest priority)
  if (shiftDuration > 0 && shiftHours < MINIMUM_WAGE_HOURS) {
    const actualHours = Math.round(shiftHours * 100) / 100
    issues.push(
      `Shift duration: ${actualHours} hours (minimum ${MINIMUM_WAGE_HOURS} hours required by law)`
    )
    issues.push(
      'Employee must be paid for minimum 3 hours regardless of actual time worked'
    )
    return { hasIssues: true, status: 'minimum-wage', issues }
  }

  // Check for shifts that are too short (but above minimum wage threshold)
  if (shiftDuration > 0 && shiftDuration < MINIMUM_SHIFT_DURATION_MINUTES) {
    const actualMinutes = Math.round(shiftDuration)
    issues.push(
      `Shift too short: ${actualMinutes} minutes (minimum ${MINIMUM_SHIFT_DURATION_MINUTES} minutes recommended)`
    )
    issues.push('Verify employee did not forget to punch in initially')
    return { hasIssues: true, status: 'too-short', issues }
  }

  return { hasIssues: false, status: 'normal', issues }
}

/**
 * Analyzes shift overlaps with detailed information
 * Fixed to properly handle early arrivals and avoid false positives
 */
const analyzeShiftOverlaps = (
  shift: AttendanceShift,
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts
): {
  hasOverlap: boolean
  overlapIssues: string[]
  conflictField?: 'start' | 'end'
} => {
  const dayShifts = attendanceData[date]?.[employeeId] || {}
  const previousDate = dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')
  const nextDate = dayjs(date).add(1, 'day').format('YYYY-MM-DD')

  const previousDayShifts = attendanceData[previousDate]?.[employeeId] || {}
  const nextDayShifts = attendanceData[nextDate]?.[employeeId] || {}

  // Find current shift key to check for overlaps
  // Use a more robust comparison that checks start/end times instead of object reference
  const currentShiftKey = Object.keys(dayShifts).find(key => {
    const dayShift = dayShifts[key]
    return (
      dayShift.start === shift.start &&
      dayShift.end === shift.end &&
      dayShift.positionId === shift.positionId
    )
  })

  console.log('🔍 analyzeShiftOverlaps - Current Shift Key Detection:', {
    employeeId,
    date,
    shiftToFind: `${formatMinutesToTime(shift.start || 0)} - ${formatMinutesToTime(shift.end || 0)} (${shift.positionId})`,
    foundKey: currentShiftKey,
    allDayShiftsDetailed: Object.entries(dayShifts).map(([key, s]) => ({
      key,
      shift: `${formatMinutesToTime(s.start || 0)} - ${formatMinutesToTime(s.end || 0)} (${s.positionId})`,
      matches:
        s.start === shift.start &&
        s.end === shift.end &&
        s.positionId === shift.positionId
    }))
  })

  const overlapIssues: string[] = []
  let hasOverlap = false
  let conflictField: 'start' | 'end' | undefined

  // Check for same-day overlaps with other distinct shifts
  const currentShiftStart = shift.start
  const currentShiftEnd = shift.end

  // If we can't find the current shift key, we can't properly detect overlaps
  if (!currentShiftKey) {
    console.warn(
      '⚠️ analyzeShiftOverlaps - Could not find current shift key, skipping overlap detection'
    )
    return { hasOverlap: false, overlapIssues: [], conflictField: undefined }
  }

  console.log('🔍 analyzeShiftOverlaps - Same Day Check:', {
    employeeId,
    date,
    currentShiftKey,
    currentShift: `${formatMinutesToTime(currentShiftStart || 0)} - ${formatMinutesToTime(currentShiftEnd || 0)}`,
    allDayShifts: Object.entries(dayShifts).map(
      ([key, s]) =>
        `${key}: ${formatMinutesToTime(s.start || 0)} - ${formatMinutesToTime(s.end || 0)}`
    )
  })

  if (currentShiftStart && currentShiftEnd) {
    Object.entries(dayShifts).forEach(([shiftKey, otherShift]) => {
      // Skip if it's the same shift or if the other shift doesn't have valid times
      if (
        shiftKey === currentShiftKey ||
        !otherShift.start ||
        !otherShift.end
      ) {
        return
      }

      const otherStart = otherShift.start
      const otherEnd = otherShift.end

      const condition1 = currentShiftStart < otherEnd
      const condition2 = currentShiftEnd > otherStart
      const touchingShifts =
        currentShiftEnd === otherStart || currentShiftStart === otherEnd

      // Check for true overlaps (not just touching shifts)
      // A true overlap means the shifts actually conflict in their working time
      // Shifts that end exactly when another starts are NOT overlapping
      const hasSignificantOverlap = condition1 && condition2 && !touchingShifts

      console.log('🔍 analyzeShiftOverlaps - Comparing Shifts:', {
        currentShift: `${currentShiftKey}: ${formatMinutesToTime(currentShiftStart)} - ${formatMinutesToTime(currentShiftEnd)}`,
        otherShift: `${shiftKey}: ${formatMinutesToTime(otherStart)} - ${formatMinutesToTime(otherEnd)}`,
        condition1: `${formatMinutesToTime(currentShiftStart)} < ${formatMinutesToTime(otherEnd)} = ${condition1}`,
        condition2: `${formatMinutesToTime(currentShiftEnd)} > ${formatMinutesToTime(otherStart)} = ${condition2}`,
        touchingShifts: `${formatMinutesToTime(currentShiftEnd)} === ${formatMinutesToTime(otherStart)} || ${formatMinutesToTime(currentShiftStart)} === ${formatMinutesToTime(otherEnd)} = ${touchingShifts}`,
        hasSignificantOverlap,
        result: hasSignificantOverlap ? '❌ OVERLAPPING' : '✅ NOT OVERLAPPING'
      })

      if (hasSignificantOverlap) {
        // Additional check: make sure this isn't just an early arrival/late departure
        // If one shift is completely contained within the scheduled time of another,
        // it might be an early arrival rather than a true overlap
        const scheduledShift = (shift as any).scheduledShift
        const otherScheduledShift = (otherShift as any).scheduledShift

        // If this shift has scheduled data and the "overlap" is just early arrival/late departure,
        // don't treat it as a true overlap
        if (
          scheduledShift &&
          currentShiftStart >= scheduledShift.start - 60 && // Within 1 hour early
          currentShiftEnd <= scheduledShift.end + 60
        ) {
          // Within 1 hour late
          // This is likely an early arrival/late departure, not a true overlap
          return
        }

        // If we get here, it's a true overlap that needs immediate attention
        hasOverlap = true

        // Determine which field has the conflict (start or end)
        // If current shift starts before other shift starts, the conflict is on the end
        // If current shift starts after other shift starts, the conflict is on the start
        conflictField = currentShiftStart < otherStart ? 'end' : 'start'

        const startTime = formatMinutesToTime(otherStart)
        const endTime = formatMinutesToTime(otherEnd)
        overlapIssues.push(`Overlaps with shift ${startTime}-${endTime}`)
      }
    })
  }

  // Check for cross-day overlaps using the existing utility
  const overlappingShifts = checkShiftOverlap({
    employeeShifts: dayShifts,
    previousDayShifts,
    nextDayShifts
  })

  if (currentShiftKey && overlappingShifts[currentShiftKey]) {
    const overlap = overlappingShifts[currentShiftKey]

    if (overlap.isStartOverlapping) {
      hasOverlap = true
      conflictField = 'start'
      overlapIssues.push('Shift start overlaps with previous day shift')
    }

    if (overlap.isEndOverlapping) {
      hasOverlap = true
      conflictField = 'end'
      overlapIssues.push('Shift end overlaps with next day shift')
    }
  }

  return { hasOverlap, overlapIssues, conflictField }
}

/**
 * Helper function to format minutes to HH:MM time string
 */
const formatMinutesToTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

/**
 * Gets user-friendly description for shift status
 */
export const getShiftStatusDescription = (status: ShiftStatus): string => {
  switch (status) {
    case 'current':
      return 'Currently working'
    case 'approved':
      return 'Shift approved'
    case 'other-role':
      return 'Different role'
    case 'problematic':
      return 'Needs manager attention'
    case 'overlapping':
      return 'Overlapping shifts - urgent'
    default:
      return 'Approved shift'
  }
}
