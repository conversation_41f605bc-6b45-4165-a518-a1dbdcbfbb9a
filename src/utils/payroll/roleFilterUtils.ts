// Role filter utilities for Payroll Hours module
import type { IPosition } from 'types/company'
import type { IEmployee } from 'types/employee'

export type DepartmentType = 'kitchen' | 'service' | 'bar' | 'management'

export type RoleFilterItem = {
  id: string
  name: string
  isSelected: boolean
  priority?: number
  subcategories: Array<{ id: string; name: string }>
}

export type DepartmentRoles = {
  [department in DepartmentType]: RoleFilterItem[]
}

export type RoleFilterState = {
  selectedDepartments: DepartmentType[]
  selectedRoles: {
    [department in DepartmentType]: string[]
  }
  selectedSubcategories: {
    [roleId: string]: string[]
  }
}

/**
 * Filter employees by selected roles
 */
export const filterEmployeesByRoles = (
  employeesByRole: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  },
  roleFilterState: RoleFilterState,
  jobs: { [roleId: string]: any }
): { [roleId: string]: { role: IPosition; employees: IEmployee[] } } => {
  const filtered: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  } = {}

  Object.entries(employeesByRole).forEach(([roleId, roleData]) => {
    // Check if this role is selected in any department
    let isRoleSelected = false

    Object.entries(roleFilterState.selectedRoles).forEach(
      ([department, selectedRoles]) => {
        if (selectedRoles.includes(roleId)) {
          isRoleSelected = true
        }
      }
    )

    if (isRoleSelected) {
      filtered[roleId] = roleData
    }
  })

  return filtered
}

/**
 * Load role filter state from localStorage
 */
export const loadRoleFilterState = (
  companyKey: string
): RoleFilterState | null => {
  try {
    const saved = localStorage.getItem(`roleFilter_${companyKey}`)
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('Failed to load role filter state:', error)
  }

  // Return null if no saved state - let caller handle default
  return null
}

/**
 * Save role filter state to localStorage
 */
export const saveRoleFilterState = (
  companyKey: string,
  state: RoleFilterState
): void => {
  try {
    localStorage.setItem(`roleFilter_${companyKey}`, JSON.stringify(state))
  } catch (error) {
    console.error('Failed to save role filter state:', error)
  }
}

/**
 * Extract roles from company data
 */
export const extractRolesFromCompany = (jobs: any): DepartmentRoles => {
  const departmentRoles: DepartmentRoles = {
    kitchen: [],
    service: [],
    bar: [],
    management: []
  }

  if (!jobs || typeof jobs !== 'object') {
    return departmentRoles
  }

  Object.entries(jobs).forEach(([jobId, job]: [string, any]) => {
    if (!job || typeof job !== 'object') return

    const { name, type, subcategories = {} } = job

    // Map job types to departments
    let department: DepartmentType
    switch (type) {
      case 'BOH':
        department = 'kitchen'
        break
      case 'FOH':
        department = 'service'
        break
      case 'BAR':
        department = 'bar'
        break
      case 'MNG':
        department = 'management'
        break
      default:
        department = 'service' // Default to service if type is unknown
    }

    // Extract subcategories
    const subcategoriesArray = Object.entries(subcategories).map(
      ([subId, sub]: [string, any]) => ({
        id: subId,
        name: sub?.name || sub?.acronym || 'Unnamed'
      })
    )

    // Create role filter item
    const roleItem: RoleFilterItem = {
      id: jobId,
      name: name || 'Unnamed Role',
      isSelected: true, // Default to selected
      priority: job.priority || 0,
      subcategories: subcategoriesArray
    }

    departmentRoles[department].push(roleItem)
  })

  // Sort roles by priority within each department
  Object.keys(departmentRoles).forEach(dept => {
    departmentRoles[dept as DepartmentType].sort(
      (a, b) => (a.priority || 0) - (b.priority || 0)
    )
  })

  return departmentRoles
}

/**
 * Check if single BOH role
 */
export const isSingleBOHRole = (
  roleFilterState: RoleFilterState,
  departmentRoles: DepartmentRoles
): boolean => {
  // Count total selected roles across all departments
  const totalSelectedRoles = Object.values(
    roleFilterState.selectedRoles
  ).reduce((total, roles) => total + roles.length, 0)

  // Check if only one role is selected and it's from kitchen (BOH)
  return (
    totalSelectedRoles === 1 &&
    roleFilterState.selectedRoles.kitchen.length === 1 &&
    roleFilterState.selectedRoles.service.length === 0 &&
    roleFilterState.selectedRoles.bar.length === 0 &&
    roleFilterState.selectedRoles.management.length === 0
  )
}

/**
 * Get single role info
 */
export const getSingleRoleInfo = (
  roleFilterState: RoleFilterState,
  departmentRoles: DepartmentRoles
): { department: DepartmentType; role: RoleFilterItem } | null => {
  // Find the department with exactly one selected role
  for (const [department, selectedRoles] of Object.entries(
    roleFilterState.selectedRoles
  )) {
    if (selectedRoles.length === 1) {
      const roleId = selectedRoles[0]
      const role = departmentRoles[department as DepartmentType]?.find(
        r => r.id === roleId
      )

      if (role) {
        return {
          department: department as DepartmentType,
          role
        }
      }
    }
  }

  return null
}

/**
 * Clear role filter state
 */
export const clearRoleFilterState = (companyKey: string): void => {
  try {
    localStorage.removeItem(`roleFilter_${companyKey}`)
  } catch (error) {
    console.error('Failed to clear role filter state:', error)
  }
}

/**
 * Create initial filter state
 */
export const createInitialFilterState = (
  departmentRoles: DepartmentRoles
): RoleFilterState => {
  // Default state - all departments and roles selected
  const selectedRoles: { [department in DepartmentType]: string[] } = {
    kitchen: departmentRoles.kitchen.map(role => role.id),
    service: departmentRoles.service.map(role => role.id),
    bar: departmentRoles.bar.map(role => role.id),
    management: departmentRoles.management.map(role => role.id)
  }

  const selectedSubcategories: { [roleId: string]: string[] } = {}

  // Select all subcategories for all roles
  Object.values(departmentRoles)
    .flat()
    .forEach(role => {
      selectedSubcategories[role.id] = role.subcategories.map(sub => sub.id)
    })

  return {
    selectedDepartments: ['kitchen', 'service', 'bar', 'management'],
    selectedRoles,
    selectedSubcategories
  }
}

/**
 * Analyze shift conflicts (simplified version)
 */
export const analyzeShiftConflicts = (
  shift: any,
  employeeId: string,
  date: string,
  attendanceData: any
): { hasConflicts: boolean; issues: string[] } => {
  // Simplified implementation - return no conflicts for now
  return {
    hasConflicts: false,
    issues: []
  }
}
