// Role filter utilities for Payroll Hours module
import type { IPosition } from 'types/company'
import type { IEmployee } from 'types/employee'

export type DepartmentType = 'kitchen' | 'service' | 'bar' | 'management'

export type RoleFilterItem = {
  id: string
  name: string
  isSelected: boolean
  priority?: number
  subcategories: Array<{ id: string; name: string }>
}

export type DepartmentRoles = {
  [department in DepartmentType]: RoleFilterItem[]
}

export type RoleFilterState = {
  selectedDepartments: DepartmentType[]
  selectedRoles: {
    [department in DepartmentType]: string[]
  }
  selectedSubcategories: {
    [roleId: string]: string[]
  }
}

/**
 * Filter employees by selected roles
 */
export const filterEmployeesByRoles = (
  employeesByRole: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  },
  roleFilterState: RoleFilterState,
  jobs: { [roleId: string]: any }
): { [roleId: string]: { role: IPosition; employees: IEmployee[] } } => {
  const filtered: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  } = {}

  Object.entries(employeesByRole).forEach(([roleId, roleData]) => {
    // Check if this role is selected in any department
    let isRoleSelected = false

    Object.entries(roleFilterState.selectedRoles).forEach(
      ([department, selectedRoles]) => {
        if (selectedRoles.includes(roleId)) {
          isRoleSelected = true
        }
      }
    )

    if (isRoleSelected) {
      filtered[roleId] = roleData
    }
  })

  return filtered
}

/**
 * Load role filter state from localStorage
 */
export const loadRoleFilterState = (companyKey: string): RoleFilterState => {
  try {
    const saved = localStorage.getItem(`roleFilter_${companyKey}`)
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('Failed to load role filter state:', error)
  }

  // Default state - all departments and roles selected
  return {
    selectedDepartments: ['kitchen', 'service', 'bar', 'management'],
    selectedRoles: {
      kitchen: [],
      service: [],
      bar: [],
      management: []
    },
    selectedSubcategories: {}
  }
}

/**
 * Save role filter state to localStorage
 */
export const saveRoleFilterState = (
  companyKey: string,
  state: RoleFilterState
): void => {
  try {
    localStorage.setItem(`roleFilter_${companyKey}`, JSON.stringify(state))
  } catch (error) {
    console.error('Failed to save role filter state:', error)
  }
}

/**
 * Extract roles from company data
 */
export const extractRolesFromCompany = (company: any): DepartmentRoles => {
  // Simplified implementation - return empty roles for now
  return {
    kitchen: [],
    service: [],
    bar: [],
    management: []
  }
}

/**
 * Check if single BOH role
 */
export const isSingleBOHRole = (
  roleFilterState: RoleFilterState,
  departmentRoles: DepartmentRoles
): boolean => {
  // Simplified implementation
  return false
}

/**
 * Get single role info
 */
export const getSingleRoleInfo = (
  roleFilterState: RoleFilterState,
  departmentRoles: DepartmentRoles
): { department: DepartmentType; role: RoleFilterItem } | null => {
  // Simplified implementation
  return null
}

/**
 * Clear role filter state
 */
export const clearRoleFilterState = (companyKey: string): void => {
  try {
    localStorage.removeItem(`roleFilter_${companyKey}`)
  } catch (error) {
    console.error('Failed to clear role filter state:', error)
  }
}

/**
 * Create initial filter state
 */
export const createInitialFilterState = (
  departmentRoles: DepartmentRoles
): RoleFilterState => {
  // Default state - all departments and roles selected
  return {
    selectedDepartments: ['kitchen', 'service', 'bar', 'management'],
    selectedRoles: {
      kitchen: [],
      service: [],
      bar: [],
      management: []
    },
    selectedSubcategories: {}
  }
}

/**
 * Analyze shift conflicts (simplified version)
 */
export const analyzeShiftConflicts = (
  shift: any,
  employeeId: string,
  date: string,
  attendanceData: any
): { hasConflicts: boolean; issues: string[] } => {
  // Simplified implementation - return no conflicts for now
  return {
    hasConflicts: false,
    issues: []
  }
}
