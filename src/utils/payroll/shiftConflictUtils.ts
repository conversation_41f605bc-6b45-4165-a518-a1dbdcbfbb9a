import dayjs from 'dayjs'

import {
  getTimeSince,
  matchClockInWithScheduledShift
} from '../../routes/PayrollOld/payrollUtils'

import { getShiftLength } from 'utils/schedule'

import { AttendanceShift } from 'types/attendance'
import { Shift } from 'types/schedule'

const CONFLICTING_SHIFT_THRESHOLD_START = 15
const CONFLICTING_SHIFT_THRESHOLD_END = 30
const MINUTES_IN_DAY = 24 * 60

/**
 * Determines if a shift is conflicting based on various criteria
 * @param shift - The attendance shift to check
 * @param shiftStartRounded - Rounded start time
 * @param shiftEndRounded - Rounded end time or null if not clocked out
 * @param scheduledShift - The scheduled shift to compare against
 * @param isToday - Whether the shift is today
 * @param isWorking - Whether the employee is currently working
 * @returns Object containing conflict information
 */
export const analyzeShiftConflicts = (
  shift: AttendanceShift,
  shiftStartRounded: number,
  shiftEndRounded: number | null,
  scheduledShift: Shift | null,
  isToday: boolean,
  isWorking: boolean
) => {
  const notClockedOut = shift.end === undefined || shift.end === null
  let isClockInDifferent = true
  let isClockOutDifferent = true

  if (scheduledShift) {
    // Check if clocked-in more than 15 minutes earlier than scheduled
    isClockInDifferent =
      scheduledShift.start - shiftStartRounded >=
      CONFLICTING_SHIFT_THRESHOLD_START

    const scheduledShiftEnd = scheduledShift.end

    if (scheduledShiftEnd === undefined || scheduledShiftEnd === null) {
      isClockOutDifferent = false
    } else {
      // Convert all times to minutes since midnight
      const shiftEndInMinutes = shiftEndRounded
      const scheduledEndInMinutes = +scheduledShiftEnd
      const shiftStartInMinutes = scheduledShift.start

      // Handle shifts that cross midnight by adding a day's worth of minutes
      const shiftEndAdjustedForOvernight =
        shiftEndInMinutes && shiftEndInMinutes < shiftStartInMinutes
          ? shiftEndInMinutes + MINUTES_IN_DAY
          : shiftEndInMinutes

      const scheduledEndAdjustedForOvernight =
        scheduledEndInMinutes < shiftStartInMinutes
          ? scheduledEndInMinutes + MINUTES_IN_DAY
          : scheduledEndInMinutes

      // Check if the clock-out time exceeded the scheduled end by more than the threshold
      if (shiftEndAdjustedForOvernight) {
        isClockOutDifferent =
          shiftEndAdjustedForOvernight - scheduledEndAdjustedForOvernight >
          CONFLICTING_SHIFT_THRESHOLD_END
      }
    }
  }

  const isConflictingShift =
    !shift.isConfirmed &&
    (isClockInDifferent ||
      isClockOutDifferent ||
      !shift.positionId ||
      (notClockedOut && !isWorking))

  return {
    isClockInDifferent,
    isClockOutDifferent,
    isConflictingShift,
    notClockedOut
  }
}

/**
 * Calculates expected shift length for ongoing shifts
 * @param isToday - Whether the shift is today
 * @param notClockedOut - Whether the employee hasn't clocked out
 * @param scheduledShift - The scheduled shift
 * @param defaultDuration - Default shift duration
 * @returns Expected shift length in minutes or null
 */
export const calculateExpectedShiftLength = (
  isToday: boolean,
  notClockedOut: boolean,
  scheduledShift: Shift | null,
  defaultDuration: number
): number | null => {
  if (isToday && notClockedOut) {
    if (scheduledShift) {
      return getShiftLength(
        scheduledShift.start,
        scheduledShift.end,
        defaultDuration
      )
    } else {
      return defaultDuration
    }
  }
  return null
}

/**
 * Determines if an employee is currently working
 * @param isToday - Whether the shift is today
 * @param notClockedOut - Whether the employee hasn't clocked out
 * @param expectedShiftLength - Expected length of the shift
 * @param shiftStartRounded - Rounded start time
 * @param timezone - Company timezone
 * @param handleError - Error handler function
 * @returns Boolean indicating if employee is working
 */
export const isEmployeeWorking = (
  isToday: boolean,
  notClockedOut: boolean,
  expectedShiftLength: number | null,
  shiftStartRounded: number,
  timezone: string,
  handleError: (error: any, start: number, timezone: string) => void
): boolean => {
  return Boolean(
    isToday &&
      notClockedOut &&
      expectedShiftLength &&
      getTimeSince(shiftStartRounded, timezone, handleError) <
        expectedShiftLength
  )
}

/**
 * Finds the best matching scheduled shift for an attendance shift
 * @param scheduledPositions - Scheduled positions for the day
 * @param shift - The attendance shift
 * @param shiftStartRounded - Rounded start time
 * @param shiftEndRounded - Rounded end time
 * @param defaultDuration - Default shift duration
 * @returns The best matching scheduled shift or null
 */
export const findMatchingScheduledShift = (
  scheduledPositions: any,
  shift: AttendanceShift,
  shiftStartRounded: number,
  shiftEndRounded: number | null,
  defaultDuration: number
): Shift | null => {
  if (!scheduledPositions || shift.start === undefined) {
    return null
  }

  const dayShifts: Shift[] = []

  Object.entries(scheduledPositions).forEach(
    ([positionId, subpositions]: [string, any]) => {
      Object.entries(subpositions).forEach(
        ([subcategoryId, subpositionShifts]: [string, any]) => {
          Object.entries(subpositionShifts).forEach(
            ([shiftKey, scheduledShift]: [string, any]) => {
              dayShifts.push({
                ...scheduledShift,
                positionId,
                subcategoryId,
                shiftKey
              })
            }
          )
        }
      )
    }
  )

  return matchClockInWithScheduledShift({
    dayShifts,
    shiftStartRounded,
    defaultDuration,
    shift: {
      start: shiftStartRounded,
      end: shiftEndRounded
    }
  })
}

/**
 * Creates conflict shift data for modal display
 * @param shift - The attendance shift
 * @param isClockInDifferent - Whether clock-in time is different
 * @param isClockOutDifferent - Whether clock-out time is different
 * @param isWorking - Whether employee is currently working
 * @param notClockedOut - Whether employee hasn't clocked out
 * @param shiftStartRounded - Rounded start time
 * @param shiftEndRounded - Rounded end time
 * @param scheduledShift - The scheduled shift
 * @returns Conflict shift data object
 */
export const createConflictShiftData = (
  shift: AttendanceShift,
  isClockInDifferent: boolean,
  isClockOutDifferent: boolean,
  isWorking: boolean,
  notClockedOut: boolean,
  shiftStartRounded: number,
  shiftEndRounded: number | null,
  scheduledShift: Shift | null
) => {
  return {
    ...shift,
    isClockInDifferent,
    isClockOutDifferent,
    neverClockedOut: isWorking ? false : notClockedOut,
    shiftStartRounded,
    shiftEndRounded,
    scheduledShift,
    missingPosition: !shift.positionId
  }
}
