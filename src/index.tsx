import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState
} from 'react'
import 'react-day-picker/style.css'
import { createRoot } from 'react-dom/client'
import { Provider, useDispatch } from 'react-redux'
import {
  I18n,
  loadTranslations,
  setLocale,
  syncTranslationWithStore
} from 'react-redux-i18n'

import {
  ErrorBoundary,
  Provider as RollbarProvider,
  useRollbarPerson
} from '@rollbar/react'

// Import test data utilities for development
import 'utils/testData'
import { updateCompanies } from 'actions/companies'
import {
  setIsLoading,
  updateCompany,
  updateCompanyEmployees,
  updateCompanyEmployeesAll,
  updateNotificationsListeners // @ts-ignore
} from 'actions/company'
import { getNotifications } from 'actions/notifications'
import 'bootstrap/dist/css/bootstrap.min.css'
import { config, rollbarConfig } from 'config'
import dayjs from 'dayjs'
import 'dayjs/locale/fr-ca'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import isBetween from 'dayjs/plugin/isBetween'
import isLeapYear from 'dayjs/plugin/isLeapYear'
import isoWeek from 'dayjs/plugin/isoWeek'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import updateLocale from 'dayjs/plugin/updateLocale'
import utc from 'dayjs/plugin/utc'
import weekday from 'dayjs/plugin/weekday'
import firebase from 'firebase/app'
import 'firebase/auth'
import 'firebase/database'
import 'firebase/storage'
import 'font-awesome/css/font-awesome.min.css'
import { find, isEmpty, map } from 'lodash'
import moment from 'moment'
// @ts-ignore
import enLocale from 'moment/locale/en-ca'
// @ts-ignore
import frLocale from 'moment/locale/fr'
import { AnyAction } from 'redux'
import { ThunkDispatch } from 'redux-thunk'
import { RootState } from 'store/reducers.js'
import {
  setHeadOfficeAccess,
  setHeadOfficeCompany
} from 'store/reducers/dashboard'
import 'toastr/build/toastr.min.css'
import { useDeepCompareEffect } from 'use-deep-compare'

import Loader from 'components/ui/Loader'

// @ts-ignore
import App from './App.js'
// @ts-ignore
import translationsObject from './i18n.js'
import './index.css'
// @ts-ignore
import { unregister } from './registerServiceWorker.js'
// @ts-ignore
import createStore from './store/createStore.js'
import { setRole } from './store/reducers/app'

import {
  FormattedRequestsToAnswer,
  RequestMap,
  combineAndFilterRequestsToAnswer
} from './utils/requests/combineAndFilterRequestsToAnswer'
import {
  initDaysJsAndMoment,
  updateDaysJsAndMomentDOW
} from 'utils/dayJsAndMoment'
import { useCheckPosOrPayrollToReconnect } from 'utils/hooks/useCheckPosOrPayrollToReconnect'

import { Company, ICompanySettings } from 'types/company'
import { IEmployee, IEmployeeRaw, IEmployeeWithRole } from 'types/employee'
import { UserWithUid } from 'types/user'

// TODO: check if shifts exchange doesn't mess up uid

// TODO: check signature for long documents
// TODO: fix shiftOpponent being a string

// web -> write to db to Push Notifications table
// web -> write a copy to a separate table to be process by backend
// that's it
// well refactor to having a week but it's too muvh
// listen for separate days -> then need to keep track of already sent notifications -> it will be a mess

// i think we will come back to how it used to be but this will be process by another function instead...

// check on call modal
// check print stuff
// check conges

// when no schedule and land on by position/by employees - should we display positions / employees?
// employee -> add forward
// decide about weekly log (?)

// TODO: handle case if they visit after some time and have to be re-authenticated

// i need to login first
// once logged in -> accept invitation
// don't allow to go anywhere until password is set
// once password is set -> redirect to the company page

// all i need is to detect if passwordless login and redirect
// if cannot change password - ask to re-login

// remove am/pm from print schedule as well

dayjs.extend(updateLocale)
dayjs.extend(advancedFormat)
dayjs.extend(localizedFormat)
dayjs.extend(isoWeek)
dayjs.extend(isLeapYear)
dayjs.extend(isBetween)
dayjs.extend(weekday)
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(relativeTime)
initDaysJsAndMoment()

firebase.initializeApp(config)

const auth = firebase.auth()
const authConst = firebase.auth
const database = firebase.database()
const databaseConst = firebase.database
const storage = firebase.storage()
const storageConst = firebase.storage

export const store = createStore()

store.dispatch(loadTranslations(translationsObject))
syncTranslationWithStore(store)

const userFromStore = store.getState().user
let locale = (userFromStore && userFromStore.locale) || 'fr'

if (userFromStore && userFromStore.locale === 'fr') {
  locale = userFromStore.locale
  // TODO: this looks weird a bit
  moment.locale(userFromStore.locale, frLocale)
  dayjs.locale('fr-ca')
}

store.dispatch(setLocale(locale))

const setAReminderToReload = () => {
  setTimeout(
    () => {
      const userWantsToReload = window.confirm(
        I18n.t(
          'common.would_you_like_to_reload_the_app_to_get_the_latest_version'
        )
      )
      if (userWantsToReload) {
        window.location.reload()
      } else {
        setAReminderToReload()
      }
    },
    15 * 60 * 1000
  )
}

if (
  process.env.REACT_APP_NODE_ENV === 'production' &&
  !process.env.REACT_APP_DEV
) {
  firebase
    .database()
    .ref('VersionWeb')
    .on('value', s => {
      const buildTimestamp = process.env.REACT_APP_BUILD_TIMESTAMP

      const dbVersionTimestamp = s.val()

      if (
        dbVersionTimestamp &&
        buildTimestamp &&
        dbVersionTimestamp > buildTimestamp
      ) {
        const userWantsToReload = window.confirm(
          I18n.t('common.new_version_released') +
            ' ' +
            I18n.t(
              'common.would_you_like_to_reload_the_app_to_get_the_latest_version'
            )
        )
        if (userWantsToReload) {
          window.location.reload()
        } else {
          // set 15 minute timer to remind user to reload
          setAReminderToReload()
        }
      }
    })
}

type PresenceStatus = {
  [key: string]: 'online' | number
} | null

const AppContext = createContext({
  employeesWithRoles: [] as IEmployeeWithRole[],
  setSelectedEmployee: (employee: IEmployeeWithRole | null) => {},
  companySettings: {} as ICompanySettings,
  currentCompany: null as Company | null,
  user: null as UserWithUid | null,
  setHeadOfficeAccessCompany: (company: Company, isHeadOffice: boolean) => {},
  shouldReconnectPos: false,
  currentEmployee: null as IEmployeeWithRole | null,
  requestsToAnswerFormatted: {
    new: {
      total: 0
    },
    pending: {
      total: 0
    }
  } as FormattedRequestsToAnswer,
  updateRequestsToAnswer: (() => {}) as React.Dispatch<
    React.SetStateAction<RequestMap>
  >,
  unreadDirectInputRequests: {} as RequestMap,
  presenceData: null as PresenceStatus
})

const useAppContext = () => {
  const context = useContext(AppContext)

  if (context === undefined) {
    throw new Error('useUserContext was used outside of its Provider')
  }

  return context
}
const Root = ({ children }: { children: any }) => {
  const [userId, setUserId] = useState<string | null>(null)
  const [isUserLoaded, setIsUserLoaded] = useState(false)
  const [user, setUser] = useState<UserWithUid | null>(null)
  const [selectedEmployee, setSelectedEmployee] =
    useState<IEmployeeWithRole | null>(null)
  const [isCompanyLoaded, setIsCompanyLoaded] = useState(false)
  const isTesting = useRef(false)
  const isEmailLink = useRef(false)
  const dispatch = useDispatch<ThunkDispatch<RootState, unknown, AnyAction>>()
  const [employeesWithRoles, setEmployeesWithRoles] = useState<
    IEmployeeWithRole[]
  >([])
  // current company is not updated when accessed via head office
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null)
  const [companySettings, setCompanySettings] = useState<ICompanySettings>({
    maxHoursPerWeek: 40
  })
  const { posToReconnect } = useCheckPosOrPayrollToReconnect(
    currentCompany?.key || ''
  )
  const [requestsToAnswer, setRequestsToAnswer] = useState<RequestMap>({})

  const [presenceData, setPresenceData] = useState<PresenceStatus>(null)
  useRollbarPerson({ id: userId })

  // Update presence status effect
  useEffect(() => {
    if (!selectedEmployee?.companyId || !selectedEmployee?.uid) return
    const presenceRef = firebase
      .database()
      .ref(
        `PresenceStatus/${selectedEmployee.companyId}/${selectedEmployee.uid}`
      )
    // Set online status when component mounts
    const setOnlineStatus = async () => {
      await presenceRef.set('online')
    }
    // Set up disconnect handler to set status to offline timestamp
    presenceRef.onDisconnect().set(firebase.database.ServerValue.TIMESTAMP)
    // Set initial online status
    setOnlineStatus()
    // Cleanup: Cancel disconnect handler when component unmounts
    return () => {
      presenceRef.onDisconnect().cancel()
    }
  }, [selectedEmployee?.companyId, selectedEmployee?.uid])

  // Listen for presence data changes
  useEffect(() => {
    if (!currentCompany?.key) return
    const presenceRef = firebase
      .database()
      .ref(`PresenceStatus/${currentCompany.key}`)
    const listener = presenceRef.on('value', snapshot => {
      const newData = snapshot.val()
      setPresenceData(newData)
    })
    // Cleanup listener on unmount
    return () => {
      presenceRef.off('value', listener)
    }
  }, [currentCompany?.key])

  useEffect(() => {
    if (currentCompany?.key) {
      const ref = firebase
        .database()
        .ref('RequestsToAnswer/' + currentCompany.key + '/company')

      ref.on('value', snapshot => {
        const data = snapshot.val() || {}
        setRequestsToAnswer(data)
      })

      return () => {
        ref.off()
      }
    }
  }, [currentCompany?.key])

  // load user and check if it's a head office email link
  useEffect(() => {
    const unsubscribe = firebase.auth().onAuthStateChanged(async user => {
      if (user) {
        let userId = user.uid

        if (user.email) {
          isEmailLink.current = await firebase
            .auth()
            .fetchSignInMethodsForEmail(user.email)
            .then(
              signInMethods =>
                signInMethods.length === 1 && signInMethods[0] === 'emailLink'
            )
        }

        if (process.env.REACT_APP_DEV) {
          if (userId === '0wztWchl2UUtUrr5jZRfn5otd0K3') {
            userId = '64jYpXQa5iTZ7FRhrO8FPDDVDS13'
          }
          // userId = '64jYpXQa5iTZ7FRhrO8FPDDVDS13' // testing both staging and production
          // userId = '7mVfGMJsAUTcBWXYQRUNGFRU7t63' // staging employee Jerome
          // userId = '0wztWchl2UUtUrr5jZRfn5otd0K3' // staging Stas Testirovich
          // store.dispatch(setLocale('en'));
          // userId = 'yKBaBuMOlgNfJfJ52rNm1By19gk1'
          // userId = '76FwCuBYBIgpcnvGvbJ1qKgjDqW2' // pivot head office
          // userId = 'QPJZNQYuPNOZhFcp0kDJjI5SC9s1' // ZIBO HEad office Prod
          // userId = 'yFTOLHG0zzPD30mbxnUWUYwUlEM2' // real ZIBO Vaudreuil Prod
          // userId = '3rhAA1jq7xUUrNisjyNj8Qvo0bW2' // <EMAIL>
          // userId = 'vaj53eknx1UwZ7t6351R8KyiWrH3' // <EMAIL>
          // userId = 'is1PF7wJPKWtMc8u6iLRXanPpoU2'
          // userId = '0XENsUatp2dY4pmP643FRaYLMFv1'
          // userId = 'DSxlgl13L0cXvB90T0SpkVEXTqx2'
          // userId='uTwySKhrG6QdOSfr8nKWGGsO6Bv1'
          // userId = 'OsmJSOEjjZVEIUNusU5GiAxVTgf2' // -NJLuNVlRi-MWSox_OTI // LUCILLE'S OYSTER MONT-TREMBLANT
          // userId = 'yLRT0SwWXFcaLzIDRIrPRbuY8bA3'
          // userId = 'Jfa1kpnQEfPc6rmL6dDBsw6Z15V2'
          // userId = 'DuNdafmn0wMBVW3Ca7tvyHH7I7g1' // Anjou zibo
          // userId = '5h51Sj5ehkML53I5SwYTQZWtaFB2' // mont-tremblant testing
          // userId = '5TwfolPKPFU0h7QJdRJ636uIKNf1' // Fromagerie Victoria Aqueduc
          // userId = 'Til3ZebzD9YxJ1pLGcJ6aqrPmXP2' // prod lucille head office
          // userId = 'gHPv7gcS3OO4WPAV1nJQ9XB1ezd2' // cafe theatre - nethris
          // userId = 'l4e1hvRTfNNLy89zGtoodZRMzLU2' // <EMAIL>
          // userId = 'OMXDcqPqvpYhobyBlqGvYRdw51B3' // new head office client
          // userId = '9S7Q75gc3Ee10JcV5iVMsQSqv6u1' // <NAME_EMAIL>
          // userId = 'iEqzxkIl5wdHU0mEwCpFoHqPYN32' // ' <EMAIL>
          // userId = 'Ku7CVbXaLBeoyYRPGValOm6PuEE2' // <NAME_EMAIL>
          // userId = 'LfOgXML3sMPJWl1z42UulTVUpFv1' // tiamo
          // userId = 'R3X13sIlokXMZtXANaP81NNx6CU2' // <EMAIL>
        }

        isTesting.current = user.uid !== userId
        setIsCompanyLoaded(false)
        setUserId(userId)
      } else {
        setUserId(null)
      }
      setIsUserLoaded(true)
    })

    return () => unsubscribe()
  }, [])

  // listen for user
  // otherwise logout
  useEffect(() => {
    if (isUserLoaded) {
      if (userId) {
        const ref = firebase.database().ref('Users/' + userId)

        // dont do this when testing other company locally
        if (!isTesting.current) {
          firebase
            .database()
            .ref('Users/' + userId)
            .update({ status: 'online' })

          ref.onDisconnect().update({
            status: 'offline',
            lastSeen: firebase.database.ServerValue.TIMESTAMP
          })
        }

        ref.on('value', async snapshot => {
          const data = snapshot.val()
          if (!isTesting.current) {
            setUser({ ...data, uid: userId })
          } else {
            // this is for testing purposes
            setUser({ ...(data || {}), uid: userId, emailVerified: true })
          }
        })
        return () => {
          ref.off()
        }
      } else {
        // logout
        setUser(null)
        setUserId(null)
        setIsUserLoaded(false)
        setSelectedEmployee(null)
        setEmployeesWithRoles([])

        dispatch(updateCompany(''))

        firebase.database().ref('Employees').off()
        firebase.database().ref('Notifications').off()

        dispatch({ type: 'LOG_OUT' })
        dispatch(setRole(''))
        setCurrentCompany(null)
        setIsCompanyLoaded(true)
      }
    }
  }, [userId, isUserLoaded, dispatch])

  // login user
  // set locale
  useEffect(() => {
    if (user && userId) {
      dispatch({
        type: 'LOG_IN',
        user: {
          ...user,
          uid: userId,
          // TODO: theorically user can have headOfficeAccess and be the owner of that branch
          // we should only set isHeadOffice if headOfficeAccess[companyId] is true
          isHeadOffice: Boolean(user.isHeadOffice || user.headOfficeAccess),
          hasNoPassword: isEmailLink.current
        }
      })

      if (
        user.locale === 'en' ||
        user.locale === 'en-ca' ||
        user.locale === 'fr' ||
        user.locale === 'fr-ca'
      ) {
        const isDev = process.env.REACT_APP_DEV
        // && false // uncomment for testing french

        // force en locale for dev
        if (!isDev) {
          // @ts-ignore
          dispatch(setLocale(user.locale))

          const isFr = user.locale !== 'en' && user.locale !== 'en-ca'
          // TODO: this looks weird a bit
          moment.updateLocale(user.locale, isFr ? frLocale : enLocale)

          dayjs.locale(isFr ? 'fr-ca' : 'en')
        } else {
          // @ts-ignore
          dispatch(setLocale('en'))
          // TODO: this looks weird a bit
          moment.updateLocale('en', enLocale)
          dayjs.locale('en')
        }
      }
    }
  }, [dispatch, user, userId])

  const hasUser = user !== null

  useDeepCompareEffect(() => {
    let isCancelled = false
    const loadData = () => {
      if (userId && hasUser) {
        const employeeRef = firebase
          .database()
          .ref('Employees')
          .orderByChild('userId')
          .equalTo(userId)

        employeeRef.on('value', async snapshot => {
          const employeesData: {
            [key: string]: IEmployeeRaw
          } = snapshot.val() || {}

          // this is either head office or owner
          // their user will be updated in the next useEffect
          // and we will setup employee listener then
          if (isEmpty(employeesData)) {
            if (user.isAdmin || user.isHeadOffice) {
              dispatch(setRole('owner'))
              employeeRef.off()
              return setIsCompanyLoaded(true)
            } else {
              dispatch(setRole('user'))
              employeeRef.off()
              return setIsCompanyLoaded(true)
            }
          }

          const employeeIds = Object.keys(employeesData || {})

          const employeesWithRoles: {
            [key: string]: IEmployeeWithRole
          } = {}

          await Promise.all(
            Object.entries(employeesData).map(
              async ([employeeId, employee]) =>
                new Promise<void>(async resolve => {
                  // head office owner
                  if (user.isHeadOffice) {
                    employeesWithRoles[employeeId] = {
                      ...employee,
                      uid: employeeId,
                      role: 'head-office'
                    }
                    return resolve()
                  }

                  // invited to head office
                  if (user.headOfficeAccess?.[employee.companyId]) {
                    employeesWithRoles[employeeId] = {
                      ...employee,
                      uid: employeeId,
                      role: 'head-office'
                    }
                    return resolve()
                  }

                  // location owner
                  if (employee.isAdmin) {
                    employeesWithRoles[employeeId] = {
                      ...employee,
                      uid: employeeId,
                      role: 'owner'
                    }
                    return resolve()
                  }

                  // no need to display companies where the employee was terminated.
                  if (!employee.positions) {
                    employeesWithRoles[employeeId] = {
                      ...employee,
                      uid: employeeId,
                      role: 'user'
                    }
                    return resolve()
                  }

                  const snapshot = await firebase
                    .database()
                    .ref('Managers/' + employee.companyId + '/' + employeeId)
                    .once('value')

                  const managerPositions = snapshot.val() || {}

                  employeesWithRoles[employeeId] = {
                    ...employee,
                    uid: employeeId,
                    role: !isEmpty(managerPositions) ? 'manager' : 'employee',
                    managerPositions
                  }

                  resolve()
                })
            )
          )

          if (!isCancelled) {
            setEmployeesWithRoles(Object.values(employeesWithRoles))

            const sorted = employeeIds.sort((a, b) =>
              (employeesData[a].lastSeen || Infinity) >
              (employeesData[b].lastSeen || Infinity)
                ? -1
                : 1
            )

            let latestEmployeeId = sorted[0]

            // if latest access is employee or user account (fired) but they have manager account in other company
            // then select that company instead
            if (
              employeesWithRoles[latestEmployeeId].role === 'employee' ||
              employeesWithRoles[latestEmployeeId].role === 'user'
            ) {
              const nonEmployeeIds = employeeIds.filter(
                id =>
                  // if employee was fired
                  employeesWithRoles[id]?.role &&
                  employeesWithRoles[id].role !== 'employee' &&
                  employeesWithRoles[id].role !== 'user'
              )

              if (nonEmployeeIds.length) {
                latestEmployeeId = nonEmployeeIds.sort((a, b) =>
                  (employeesData[b].lastSeen || Infinity) >
                  (employeesData[a].lastSeen || Infinity)
                    ? -1
                    : 1
                )[0]
              }
            }

            if (latestEmployeeId) {
              const companyId = sessionStorage.getItem('companyId')
              const latestEmployee = employeesWithRoles[latestEmployeeId]

              // if head office and companyId is set in session storage
              // meaning user accessed a branch company from head office
              // then check if that user has access to the company
              if (latestEmployee.role === 'head-office' && companyId) {
                const headOfficeCompany = (
                  await firebase
                    .database()
                    .ref(`Companies/${latestEmployee.companyId}`)
                    .once('value')
                ).val()

                const hasAccess = Boolean(
                  headOfficeCompany?.locations?.[companyId]
                )

                if (hasAccess) {
                  const company = (
                    await firebase
                      .database()
                      .ref(`Companies/${companyId}`)
                      .once('value')
                  ).val()

                  dispatch(
                    setHeadOfficeCompany({
                      ...headOfficeCompany,
                      key: latestEmployee.companyId
                    })
                  )

                  setHeadOfficeAccessCompany(
                    {
                      ...company,
                      key: companyId
                    },
                    true
                  )
                } else {
                  setSelectedEmployee(latestEmployee)
                }
              } else {
                setSelectedEmployee(latestEmployee)
              }
            }
          }
        })

        return () => {
          isCancelled = true
          if (employeeRef) {
            employeeRef.off()
          }
        }
      }
    }

    loadData()
  }, [
    userId,
    hasUser,
    user?.isAdmin,
    user?.isHeadOffice,
    user?.headOfficeAccess,
    user?.companiesCreated,
    dispatch
  ])

  useEffect(() => {
    if (selectedEmployee?.companyId && selectedEmployee?.role) {
      let companysRef = firebase
        .database()
        .ref('Companies/' + selectedEmployee.companyId)

      let companySettingsRef = firebase
        .database()
        .ref('CompanySettings/' + selectedEmployee.companyId)

      dispatch(
        setRole(selectedEmployee.role, selectedEmployee.managerPositions)
      )

      companysRef.on(
        'value',
        async snapshot => {
          const companyData = snapshot.val()
          if (companyData) {
            const currentCompany = {
              ...companyData,
              key: snapshot.key
            }

            updateDaysJsAndMomentDOW(currentCompany)

            dispatch(updateCompanies([currentCompany]))
            setCurrentCompany(currentCompany)
          }
          setIsCompanyLoaded(true)
        },
        error => {
          console.log(error)
        }
      )

      companySettingsRef.on('value', s => {
        const dbSettings = s.val() || {}

        setCompanySettings({
          ...dbSettings,
          maxHoursPerWeek: dbSettings.maxHoursPerWeek || 40
        })
      })

      return () => {
        if (companysRef) {
          companysRef.off()
        }
        if (companySettingsRef) {
          companySettingsRef.off()
        }
      }
    }
  }, [
    selectedEmployee?.role,
    selectedEmployee?.companyId,
    selectedEmployee?.managerPositions,
    dispatch
  ])

  useEffect(() => {
    let employeeUnsubscribe: null | (() => void) = null

    if (currentCompany?.key) {
      dispatch(setIsLoading())

      const employeesRef = firebase
        .database()
        .ref('Employees')
        .orderByChild('companyId')
        .equalTo(currentCompany.key)

      const callback = (snapshot: {
        val: () => Record<string, IEmployeeRaw>
      }) => {
        const employeesData = snapshot.val()
        let employees: {
          [key: string]: IEmployee
        } = {}
        if (employeesData) {
          const employeesWithUid = map(
            employeesData,
            (employee, employeeId) => {
              return {
                ...employee,
                uid: employeeId
              }
            }
          )

          const employeesObject: {
            [key: string]: IEmployee
          } = {}
          const employeesAll: {
            [key: string]: IEmployee
          } = {}

          employeesWithUid
            .filter(employee => employee)
            .forEach(employee => {
              if (
                employee!.isAdmin ||
                (employee!.positions && employee!.positions.length > 0)
              ) {
                employeesObject[employee!.uid] = employee!
              }
              employeesAll[employee!.uid] = employee!
            })

          dispatch(updateCompanyEmployees(employeesObject))
          dispatch(updateCompanyEmployeesAll(employeesAll))
        } else {
          dispatch(updateCompanyEmployees(employees))
        }
      }

      employeesRef.on('value', callback)
      employeeUnsubscribe = () => employeesRef.off('value', callback)

      dispatch(updateCompany(currentCompany.key))
    }
    return () => {
      if (employeeUnsubscribe) {
        employeeUnsubscribe()
      }
    }
  }, [currentCompany?.key, dispatch])

  // loading notifications
  useEffect(() => {
    const notificationsUnsubscribe: (() => void)[] = []

    if (selectedEmployee?.uid && currentCompany) {
      dispatch(getNotifications(selectedEmployee.uid, currentCompany))
      dispatch(
        updateNotificationsListeners(
          selectedEmployee.uid,
          currentCompany.key,
          notificationsUnsubscribe
        )
      )
    }

    return () => {
      notificationsUnsubscribe.forEach(unsubscribe => unsubscribe())
    }
  }, [selectedEmployee?.uid, currentCompany, dispatch])

  // isHeadOffice is true when accessing some branch company from head office
  // isHeadOffice is false when returning back to head office
  const setHeadOfficeAccessCompany = async (
    company: Company,
    isHeadOffice: boolean
  ) => {
    setIsCompanyLoaded(false)

    sessionStorage.setItem('companyId', company?.key)

    // if isHeadOffice access is true
    // then use employee that equals createdBy in a company
    const userIdToUse = isHeadOffice ? company.createdBy : userId
    const employees: Record<string, IEmployeeRaw> = (
      await firebase
        .database()
        .ref('Employees/')
        .orderByChild('userId')
        .equalTo(userIdToUse)
        .once('value')
    ).val()

    const employeesWithRoles: {
      [key: string]: IEmployeeWithRole
    } = {}

    find(employees, (employee, employeeId) => {
      if (employee.companyId === company.key) {
        employeesWithRoles[employeeId] = {
          ...employee,
          uid: employeeId,
          role: isHeadOffice ? 'owner' : 'head-office'
        }
        return true
      }
      return false
    })

    setEmployeesWithRoles(Object.values(employeesWithRoles))

    const latestEmployeeId = Object.keys(employeesWithRoles)[0]

    if (latestEmployeeId) {
      setSelectedEmployee(employeesWithRoles[latestEmployeeId])
    }

    if (!isHeadOffice) {
      sessionStorage.removeItem('companyId')
    }

    dispatch(setHeadOfficeAccess(isHeadOffice))
  }

  // add loading screen here
  if (!isCompanyLoaded) {
    return <Loader />
  }

  const filteredRequestsToAnswer = combineAndFilterRequestsToAnswer(
    requestsToAnswer,
    selectedEmployee
  )

  const unreadDirectInputRequests = Object.entries(requestsToAnswer).reduce(
    (acc, [key, request]) => {
      if (request.type === 'applicant') {
        acc[key] = request
      }
      return acc
    },
    {} as RequestMap
  )

  return (
    <AppContext.Provider
      value={{
        employeesWithRoles,
        setSelectedEmployee,
        currentCompany,
        currentEmployee: selectedEmployee,
        companySettings,
        user,
        setHeadOfficeAccessCompany,
        shouldReconnectPos: posToReconnect.length > 0,
        requestsToAnswerFormatted: filteredRequestsToAnswer,
        updateRequestsToAnswer: setRequestsToAnswer,
        unreadDirectInputRequests,
        presenceData
      }}
    >
      {children}
    </AppContext.Provider>
  )
}

export {
  auth,
  authConst,
  database,
  databaseConst,
  storage,
  storageConst,
  useAppContext,
  AppContext
}

const container = document.getElementById('root')
if (!container) throw new Error('Failed to find the root element')
const root = createRoot(container)

root.render(
  <RollbarProvider config={rollbarConfig}>
    <ErrorBoundary>
      <Provider store={store}>
        <Root>
          <App />
        </Root>
      </Provider>
    </ErrorBoundary>
  </RollbarProvider>
)
unregister()
