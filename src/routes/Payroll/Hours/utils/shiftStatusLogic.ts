import dayjs from 'dayjs'
import { isEmpty, forEach, isNumber } from 'lodash'
import { checkShiftOverlap } from 'utils/attendance'
import { getShiftLength, getBreaksLength, roundTime } from 'routes/PayrollOld/payrollUtils'
import { matchClockInWithScheduledShift } from 'routes/PayrollOld/payrollUtils'
import { getTimeSince } from 'utils/time'
import type { AttendanceShift, AttendanceShifts } from 'types/attendance'
import type { Company } from 'types/company'
import type { Shift } from 'types/schedule'

// Constants from PayrollOld
const CONFLICTING_SHIFT_THRESHOLD_START = 15 // 15 minutes for early arrival
const CONFLICTING_SHIFT_THRESHOLD_END = 30 // 30 minutes for late departure

export type ShiftStatus = 
  | 'working' // Currently working (blue)
  | 'confirmed' // Confirmed shift (green/white)
  | 'conflicting' // Conflicting shift (orange)
  | 'overlapping' // Overlapping shift (red)
  | 'not-clocked' // Never clocked out (pink)

export type ShiftAnalysisResult = {
  status: ShiftStatus
  isConflictingShift: boolean
  isClockInDifferent: boolean
  isClockOutDifferent: boolean
  isStartOverlapping: boolean
  isEndOverlapping: boolean
  isWorking: boolean
  notClockedOut: boolean
  isConfirmed: boolean
  shiftLengthHours: number
  issues: string[]
}

/**
 * Main function to analyze shift status - mirrors PayrollOld logic exactly
 */
export const analyzeShiftStatus = (
  shift: AttendanceShift,
  shiftKey: string,
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts,
  schedule: any,
  currentCompany: Company,
  roundingTime: number = 5,
  defaultDuration: number = 390, // 6.5 hours in minutes
  isToday: boolean = false
): ShiftAnalysisResult => {
  const issues: string[] = []
  
  // Round shift times (from PayrollOld logic)
  const shiftStartRounded = shift.start ? roundTime(shift.start, roundingTime) : 0
  const shiftEndRounded = shift.end ? roundTime(shift.end, roundingTime) : null
  
  const notClockedOut = shift.start && !shift.end

  // Get overlapping shifts data (from PayrollOld logic)
  const dayEmployees = attendanceData[date] || {}
  const employeeShifts = dayEmployees[employeeId] || {}
  
  const nextDayShifts = attendanceData[dayjs(date).add(1, 'day').format('YYYY-MM-DD')]?.[employeeId] || {}
  const previousDayShifts = attendanceData[dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')]?.[employeeId] || {}
  
  // Check for overlaps using PayrollOld logic
  const shiftsToCheck = {
    employeeShifts,
    nextDayShifts,
    previousDayShifts
  }
  
  const overlappedShifts = checkShiftOverlap(shiftsToCheck)
  const isStartOverlapping = overlappedShifts[shiftKey]?.isStartOverlapping || false
  const isEndOverlapping = overlappedShifts[shiftKey]?.isEndOverlapping || false
  const isOverlapping = isStartOverlapping || isEndOverlapping

  // Clock in/out difference detection (from PayrollOld logic)
  let isClockInDifferent = false
  let isClockOutDifferent = false
  let shiftToCheckAgainst = null

  const scheduledPositions = schedule?.[date]?.[employeeId] || {}

  if (!isEmpty(scheduledPositions) && shift.start !== undefined) {
    const dayShifts: Shift[] = []

    forEach(scheduledPositions, (subpositions, positionId) => {
      forEach(subpositions, (subpositionShifts, subcategoryId) => {
        forEach(subpositionShifts, (scheduledShift, shiftKey) => {
          dayShifts.push({
            ...scheduledShift,
            positionId,
            subcategoryId,
            shiftKey
          })
        })
      })
    })

    const matchedShift = matchClockInWithScheduledShift({
      dayShifts,
      shiftStartRounded,
      defaultDuration,
      shift: {
        start: shiftStartRounded,
        end: shiftEndRounded || undefined
      }
    })

    if (matchedShift) {
      shiftToCheckAgainst = matchedShift
      
      // Check clock in difference (from PayrollOld logic)
      const scheduledStartAdjustedForOvernight = matchedShift.start < shiftStartRounded 
        ? matchedShift.start + 24 * 60 
        : matchedShift.start
      
      isClockInDifferent = Math.abs(shiftStartRounded - scheduledStartAdjustedForOvernight) > CONFLICTING_SHIFT_THRESHOLD_START

      // Check clock out difference (from PayrollOld logic)
      if (shiftEndRounded && matchedShift.end) {
        const scheduledEndAdjustedForOvernight = matchedShift.end < matchedShift.start 
          ? matchedShift.end + 24 * 60 
          : matchedShift.end
        
        const shiftEndAdjustedForOvernight = shiftEndRounded < shiftStartRounded 
          ? shiftEndRounded + 24 * 60 
          : shiftEndRounded

        isClockOutDifferent = shiftEndAdjustedForOvernight - scheduledEndAdjustedForOvernight > CONFLICTING_SHIFT_THRESHOLD_END
      }
    }
  } else {
    // No scheduled shift - use default logic from PayrollOld
    isClockInDifferent = false
    isClockOutDifferent = false
  }

  // Working status (from PayrollOld logic)
  let expectedShiftLength = null
  if (isToday && notClockedOut) {
    if (shiftToCheckAgainst) {
      expectedShiftLength = getShiftLength(
        shiftToCheckAgainst.start,
        shiftToCheckAgainst.end,
        defaultDuration
      )
    } else {
      expectedShiftLength = defaultDuration
    }
  }

  const isWorking = Boolean(
    isToday &&
    notClockedOut &&
    expectedShiftLength &&
    getTimeSince(shiftStartRounded, currentCompany.timezone, () => {}) < expectedShiftLength
  )

  // Conflicting shift logic (from PayrollOld logic)
  const isConflictingShift = !shift.isConfirmed && (
    isClockInDifferent ||
    isClockOutDifferent ||
    !shift.positionId ||
    (notClockedOut && !isWorking)
  )

  // Calculate shift length
  let shiftLength = isNumber(shiftStartRounded) && isNumber(shiftEndRounded)
    ? getShiftLength(shiftStartRounded, shiftEndRounded, defaultDuration)
    : 0

  const breaksLength = getBreaksLength(shift.breaks, roundingTime)
  if (shiftLength && breaksLength) {
    shiftLength -= breaksLength
    if (shiftLength < 0) {
      shiftLength = 0
    }
  }

  const shiftLengthHours = Math.round((shiftLength / 60) * 100) / 100

  // Confirmed status (from PayrollOld logic)
  const isConfirmed = Boolean(
    !isWorking &&
    (shift.isConfirmed || !isConflictingShift) &&
    !isOverlapping
  )

  // Determine final status (from PayrollOld logic)
  let status: ShiftStatus
  if (isWorking) {
    status = 'working'
  } else if (notClockedOut) {
    status = 'not-clocked'
  } else if (isOverlapping) {
    status = 'overlapping'
  } else if (isConflictingShift) {
    status = 'conflicting'
  } else {
    status = 'confirmed'
  }

  // Build issues array
  if (isClockInDifferent) {
    issues.push('Employee arrived early - confirm authorization')
  }
  if (isClockOutDifferent) {
    issues.push('Employee left later than scheduled')
  }
  if (!shift.positionId) {
    issues.push('No position assigned')
  }
  if (notClockedOut && !isWorking) {
    issues.push('Employee forgot to punch out')
  }
  if (isOverlapping) {
    issues.push('Shift overlaps with another shift')
  }

  return {
    status,
    isConflictingShift,
    isClockInDifferent,
    isClockOutDifferent,
    isStartOverlapping,
    isEndOverlapping,
    isWorking,
    notClockedOut: Boolean(notClockedOut),
    isConfirmed,
    shiftLengthHours,
    issues
  }
}

/**
 * Get color for shift status (mirrors PayrollOld styling)
 */
export const getShiftStatusColor = (analysis: ShiftAnalysisResult): string => {
  switch (analysis.status) {
    case 'working':
      return 'blue' // Currently working
    case 'confirmed':
      return 'white' // Confirmed shift
    case 'conflicting':
      return 'orange' // Conflicting shift
    case 'overlapping':
      return 'red' // Overlapping shift
    case 'not-clocked':
      return 'red' // Never clocked out
    default:
      return 'white'
  }
}
