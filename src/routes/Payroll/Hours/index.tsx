import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import firebase from 'firebase/app'
import 'firebase/database'
import { cloneDeep, isEmpty } from 'lodash'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { useAppContext } from 'contexts/AppContext'
import { usePeriod } from 'contexts/PeriodContext'
import { useOnlineStatus } from 'hooks/useOnlineStatus'

import { RootState } from 'store/reducers'
import { AttendanceSettings, Company, IEmployee } from 'types'
import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { IAllConflictingAttendanceShifts } from 'types/payroll'

import { analyzeShiftConflicts } from 'utils/payroll/conflictAnalysis'
import { getCurrentUserInfo } from 'utils/payroll/userInfo'
import { logShiftChanges, logShiftDeletion } from 'utils/payroll/shiftLogging'
import {
  createInitialFilterState,
  loadRoleFilterState,
  saveRoleFilterState,
  clearRoleFilterState,
  isSingleBOHRole,
  getSingleRoleInfo,
  extractRolesFromCompany
} from 'utils/payroll/roleFiltering'
import { RoleFilterState } from 'types/roleFiltering'

import CalendarPopover from './components/CalendarPopover'
import HoursTable from './components/HoursTable'
import PayrollConflictModal from '../PayrollOld/modals/PayrollConflictModal'
import RollingNumber from './components/RollingNumber'

import { ReactComponent as ClockIcon } from 'img/icons/hoursIcon.svg'
import { ReactComponent as SparkleIcon } from 'img/icons/sparkleIcon.svg'

const database = firebase.database()
const maxRetries = 3

const Hours: React.FC = () => {
  const { user, currentEmployee } = useAppContext()
  const isOnline = useOnlineStatus()

  // Error and retry state
  const [dataError, setDataError] = useState<Error | null>(null)
  const [scheduleError, setScheduleError] = useState<Error | null>(null)
  const [isRetrying, setIsRetrying] = useState(false)

  // Retry utility function with exponential backoff
  const retryWithBackoff = useCallback(async (
    operation: () => Promise<void>,
    attempt: number = 1
  ): Promise<void> => {
    try {
      await operation()
    } catch (error) {
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000 // Exponential backoff
        console.log(`Retry attempt ${attempt} failed, retrying in ${delay}ms...`)
        setTimeout(() => retryWithBackoff(operation, attempt + 1), delay)
      } else {
        console.error('Max retries reached, operation failed:', error)
        throw error
      }
    }
  }, [])

  // Handle retry for data fetching
  const handleRetry = useCallback(() => {
    if (isRetrying) return

    setIsRetrying(true)
    setDataError(null)
    setScheduleError(null)

    const retryOperation = async () => {
      try {
        // Retry attendance data fetch
        if (dataError) {
          console.log('Retrying attendance data fetch...')
          setDataError(null)
        }

        // Retry schedule data fetch
        if (scheduleError) {
          console.log('Retrying schedule data fetch...')
          setScheduleError(null)
        }

        setIsRetrying(false)
      } catch (error) {
        setIsRetrying(false)
        throw error
      }
    }

    retryWithBackoff(retryOperation)
  }, [isRetrying, dataError, scheduleError, retryWithBackoff])

  // Auto-retry logic
  useEffect(() => {
    if ((dataError || scheduleError) && !isRetrying && isOnline) {
      const retryTimer = setTimeout(() => {
        console.log('Auto-retrying after error...')
        handleRetry()
      }, 5000) // Auto-retry after 5 seconds

      return () => clearTimeout(retryTimer)
    }
  }, [dataError, scheduleError, isRetrying, isOnline, handleRetry])

  // Get current company and related data
  const currentCompany: Company = useSelector((state: RootState) => state.currentCompany)
  const employeesArray: IEmployee[] = useSelector((state: RootState) => state.employees)
  const attendanceSettings: AttendanceSettings = useSelector((state: RootState) => state.attendanceSettings)
  const hasPayrollIntegration = !isEmpty(currentCompany?.payrollIntegration)

  // Period management
  const {
    startOfPeriod,
    currentPeriodOffset,
    payrollLength,
    filteredPeriodData
  } = usePeriod()

  // Calculate period strings for data fetching
  const startOfPeriodStr = startOfPeriod.format('YYYY-MM-DD')
  const endOfPeriodStr = startOfPeriod.clone().add(payrollLength - 1, 'days').format('YYYY-MM-DD')

  // State management
  const [searchTerm, setSearchTerm] = useState('')
  const [displayBy, setDisplayBy] = useState('employee')
  const [selectedPositionId, setSelectedPositionId] = useState<string>('')
  const [showConflictModal, setShowConflictModal] = useState(false)
  const [allConflictingShifts, setAllConflictingShifts] = useState<IAllConflictingAttendanceShifts>({})
  const [claimedAmount, setClaimedAmount] = useState(0)
  const [animationKey, setAnimationKey] = useState(0)

  // Role filtering state
  const [roleFilterState, setRoleFilterState] = useState<RoleFilterState>(() =>
    createInitialFilterState(currentCompany)
  )

  // Load saved role filter state
  useEffect(() => {
    const savedState = loadRoleFilterState(currentCompany?.key)
    if (savedState) {
      setRoleFilterState(savedState)
    }
  }, [currentCompany?.key])

  // Save role filter state when it changes
  useEffect(() => {
    if (currentCompany?.key) {
      saveRoleFilterState(currentCompany.key, roleFilterState)
    }
  }, [currentCompany?.key, roleFilterState])

  // Clear role filter state when switching to employee view
  useEffect(() => {
    if (displayBy === 'employee') {
      const clearedState = clearRoleFilterState(currentCompany)
      setRoleFilterState(clearedState)
    }
  }, [displayBy, currentCompany])

  // Helper functions for role filtering
  const isSingleRole = isSingleBOHRole(roleFilterState)
  const singleRoleInfo = isSingleRole ? getSingleRoleInfo(roleFilterState, currentCompany) : null

  // Data fetching and processing
  const [attendanceData, setAttendanceData] = useState<AttendanceShifts>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)

  // Fetch attendance data
  useEffect(() => {
    if (!currentCompany?.key || !startOfPeriodStr || !endOfPeriodStr) {
      setIsDataLoaded(false)
      return
    }

    setIsDataLoaded(false)
    setDataError(null)

    const fetchAttendanceData = async () => {
      try {
        console.log(`Fetching attendance data for period: ${startOfPeriodStr} to ${endOfPeriodStr}`)

        const attendanceRef = database.ref(`Attendance/${currentCompany.key}`)
        const snapshot = await attendanceRef.once('value')
        const allData = snapshot.val() || {}

        // Filter data for the current period
        const periodData: AttendanceShifts = {}
        const startDate = dayjs(startOfPeriodStr)
        const endDate = dayjs(endOfPeriodStr)

        for (let date = startDate; date.isSameOrBefore(endDate); date = date.add(1, 'day')) {
          const dateStr = date.format('YYYY-MM-DD')
          if (allData[dateStr]) {
            periodData[dateStr] = allData[dateStr]
          }
        }

        setAttendanceData(periodData)
        setIsDataLoaded(true)
        console.log('Attendance data loaded successfully')
      } catch (error) {
        console.error('Error fetching attendance data:', error)
        setDataError(error as Error)
        setIsDataLoaded(false)
      }
    }

    fetchAttendanceData()
  }, [currentCompany?.key, startOfPeriodStr, endOfPeriodStr])

  // Filter attendance data based on current filters
  const filteredAttendanceData = useMemo(() => {
    if (!attendanceData) return {}

    // Apply role filtering if in role view
    if (displayBy === 'role') {
      const filteredData: AttendanceShifts = {}

      Object.entries(attendanceData).forEach(([date, dayData]) => {
        const filteredDayData: { [employeeId: string]: { [shiftId: string]: AttendanceShift } } = {}

        Object.entries(dayData || {}).forEach(([employeeId, employeeShifts]) => {
          const employee = employeesArray.find(emp => emp.uid === employeeId)
          if (!employee) return

          // Check if employee's role is selected in filter
          const employeeRoles = extractRolesFromCompany(currentCompany, employee)
          const hasSelectedRole = employeeRoles.some(role =>
            roleFilterState.selectedRoles[role.id] === true
          )

          if (hasSelectedRole) {
            filteredDayData[employeeId] = employeeShifts
          }
        })

        if (Object.keys(filteredDayData).length > 0) {
          filteredData[date] = filteredDayData
        }
      })

      return filteredData
    }

    return attendanceData
  }, [attendanceData, displayBy, roleFilterState, employeesArray, currentCompany])

  // Search and filter employees
  const onSearchEmployee = useCallback((term: string) => {
    setSearchTerm(term)
  }, [])

  // Position filtering
  const _setSelectedPositionId = useCallback((positionId: string) => {
    setSelectedPositionId(positionId)
  }, [])

  // Display options
  const displayByArray = [
    { value: 'employee', label: I18n.t('payroll.by_employee') },
    { value: 'role', label: I18n.t('payroll.by_roles') }
  ]

  // Group employees by role for role view
  const employeesByRole = useMemo(() => {
    if (displayBy !== 'role') return {}

    const grouped: { [roleId: string]: IEmployee[] } = {}

    employeesArray.forEach(employee => {
      const roles = extractRolesFromCompany(currentCompany, employee)
      roles.forEach(role => {
        if (roleFilterState.selectedRoles[role.id]) {
          if (!grouped[role.id]) {
            grouped[role.id] = []
          }
          grouped[role.id].push(employee)
        }
      })
    })

    return grouped
  }, [employeesArray, currentCompany, displayBy, roleFilterState])

  // Conflict detection and analysis
  useEffect(() => {
    if (!isDataLoaded || !filteredAttendanceData) return

    const conflicts = analyzeShiftConflicts(filteredAttendanceData, employeesArray)
    setAllConflictingShifts(conflicts)
  }, [filteredAttendanceData, employeesArray, isDataLoaded])

  // Calculate number of conflicts
  const numberOfConflicts = useMemo(() => {
    return Object.values(allConflictingShifts).reduce((total, dayConflicts) => {
      return total + Object.keys(dayConflicts).length
    }, 0)
  }, [allConflictingShifts])

  // Save and delete handlers
  const onSave = useCallback(async (
    employeeId: string,
    date: string,
    shiftId: string,
    shiftData: Partial<AttendanceShift>,
    originalShift?: AttendanceShift
  ) => {
    if (!currentCompany?.key) return

    try {
      const path = `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftId}`
      await database.ref(path).update(shiftData)

      // Log the change
      const employee = employeesArray.find(emp => emp.uid === employeeId)
      if (employee && originalShift) {
        await logShiftChanges(
          getCurrentUserInfo(user, currentEmployee),
          employee,
          originalShift,
          { ...originalShift, ...shiftData },
          currentCompany.key
        )
      }

      // Update local state
      setAttendanceData(prev => ({
        ...prev,
        [date]: {
          ...prev[date],
          [employeeId]: {
            ...prev[date]?.[employeeId],
            [shiftId]: { ...prev[date]?.[employeeId]?.[shiftId], ...shiftData }
          }
        }
      }))

      console.log('Shift saved successfully')
    } catch (error) {
      console.error('Error saving shift:', error)
    }
  }, [currentCompany?.key, employeesArray, user, currentEmployee])

  const onDeleteShift = useCallback(async (
    employeeId: string,
    date: string,
    shiftId: string,
    shift: AttendanceShift
  ) => {
    if (!currentCompany?.key) return

    try {
      const path = `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftId}`
      await database.ref(path).remove()

      // Log the deletion
      const employee = employeesArray.find(emp => emp.uid === employeeId)
      if (employee) {
        await logShiftDeletion(
          getCurrentUserInfo(user, currentEmployee),
          employee,
          shift,
          currentCompany.key
        )
      }

      // Update local state
      setAttendanceData(prev => {
        const newData = { ...prev }
        if (newData[date]?.[employeeId]) {
          const { [shiftId]: deleted, ...remainingShifts } = newData[date][employeeId]
          if (Object.keys(remainingShifts).length === 0) {
            const { [employeeId]: deletedEmployee, ...remainingEmployees } = newData[date]
            if (Object.keys(remainingEmployees).length === 0) {
              const { [date]: deletedDate, ...remainingDates } = newData
              return remainingDates
            } else {
              newData[date] = remainingEmployees
            }
          } else {
            newData[date][employeeId] = remainingShifts
          }
        }
        return newData
      })

      console.log('Shift deleted successfully')
    } catch (error) {
      console.error('Error deleting shift:', error)
    }
  }, [currentCompany?.key, employeesArray, user, currentEmployee])

  // Animation trigger for claimed amount
  const handleMouseEnter = useCallback(() => {
    setAnimationKey(prev => prev + 1)
  }, [])

  // Week display logic
  const staticWeekDays = useMemo(() => {
    const startOfWeek = dayjs().startOf('week').add(1, 'day') // Start from Monday
    return Array.from({ length: 7 }, (_, index) => {
      const day = startOfWeek.add(index, 'day')
      return {
        name: day.format('ddd'),
        date: day.format('DD'),
        fullDate: day.format('YYYY-MM-DD')
      }
    })
  }, [])

  const currentDayIndex = useMemo(() => {
    const today = dayjs()
    const startOfWeek = dayjs().startOf('week').add(1, 'day')
    return today.diff(startOfWeek, 'day')
  }, [])

  // Loading and error states
  if (!isDataLoaded && !dataError) {
    return (
      <LoadingContainer>
        <LoadingSpinner />
        <div className="loading-text">
          {I18n.t('payroll.loading_attendance_data')}
        </div>
      </LoadingContainer>
    )
  }

  if (dataError && !isRetrying) {
    return (
      <ErrorContainer>
        <div className="error-text">
          {I18n.t('payroll.error_loading_data')}
        </div>
        <button onClick={handleRetry} className="retry-button">
          {I18n.t('payroll.retry')}
        </button>
      </ErrorContainer>
    )
  }

  return (
    <>
      <WeekBlockWrapStyled>
        <WeekBlockStyled>
          <WeekTopBlockStyled>
            <WeekTopBlockRowStyled>
              <CalendarPopover />
              <WeekPeriodTabsStyled>
                {payrollLength === 14 && (
                  <>
                    <WeekPeriodTabStyled
                      $isActive={true}
                      onClick={() => {}}
                    >
                      {I18n.t('payroll.week')} 1
                    </WeekPeriodTabStyled>
                    <WeekPeriodTabStyled
                      $isActive={false}
                      onClick={() => {}}
                    >
                      {I18n.t('payroll.week')} 2
                    </WeekPeriodTabStyled>
                  </>
                )}
              </WeekPeriodTabsStyled>
            </WeekTopBlockRowStyled>
            <WeekTopBlockRowStyled>
              <ClaimedStatusStyled onMouseEnter={handleMouseEnter}>
                <SparkleIconStyled key={animationKey} />
                <ClaimedStatusBlockStyled>
                  <ClaimedTextStyled>
                    {I18n.t('payroll.you_saved')}
                  </ClaimedTextStyled>
                  <RollingNumber value={claimedAmount} />
                </ClaimedStatusBlockStyled>
              </ClaimedStatusStyled>

              <ButtonStyled
                $isOrange={numberOfConflicts > 0}
                onClick={() => setShowConflictModal(true)}
              >
                <ClockIconStyled />
                {I18n.t('payroll.conflicting_shifts')}
                <span>{numberOfConflicts}</span>
              </ButtonStyled>
            </WeekTopBlockRowStyled>
          </WeekTopBlockStyled>
          <WeekDaysStyled>
            {staticWeekDays.map((day, index) => (
              <DayStyled
                $isToday={index === currentDayIndex}
                key={index}
              >
                {day.name} {day.date}
              </DayStyled>
            ))}
          </WeekDaysStyled>
        </WeekBlockStyled>
      </WeekBlockWrapStyled>

      <HoursTable
        employeesArray={employeesArray.map((employee, index) => ({
          id: index + 1,
          name: employee.name || '',
          surname: employee.surname || '',
          email: employee.email || '',
          uid: employee.uid || '',
          customId: employee.customId
        }))}
        employeesByRole={employeesByRole}
        searchEmployee={searchTerm}
        onSearchEmployee={onSearchEmployee}
        displayBy={displayBy}
        setDisplayBy={setDisplayBy}
        displayByArray={displayByArray}
        attendanceData={filteredAttendanceData}
        currentCompany={currentCompany}
        isDataLoaded={isDataLoaded}
        selectedPositionId={selectedPositionId}
        setSelectedPositionId={_setSelectedPositionId}
        startOfPeriod={filteredPeriodData.startOfPeriod}
        currentPeriodOffset={currentPeriodOffset}
        payrollLength={filteredPeriodData.payrollLength}
        onSave={onSave}
        onDeleteShift={onDeleteShift}
        roleFilterState={roleFilterState}
        setRoleFilterState={setRoleFilterState}
        isSingleRole={isSingleRole}
        singleRoleInfo={singleRoleInfo}
        hasPayrollIntegration={hasPayrollIntegration}
      />

      <PayrollConflictModal
        show={showConflictModal}
        onHide={() => setShowConflictModal(false)}
        conflicts={cloneDeep(allConflictingShifts)}
        roundingTime={15}
        companyId={currentCompany?.key || ''}
        jobs={currentCompany?.jobs || {}}
      />
    </>
  )
}

export default Hours

// Styled Components
const WeekBlockWrapStyled = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 1rem;
`

const WeekBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`

const WeekTopBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
`

const WeekTopBlockRowStyled = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const WeekPeriodTabsStyled = styled.div`
  display: flex;
  gap: 0.5rem;
`

const WeekPeriodTabStyled = styled.button<{ $isActive: boolean }>`
  padding: 0.5rem 1rem;
  border: 1px solid ${props => props.$isActive ? theme.colors.blue : '#ddd'};
  background: ${props => props.$isActive ? theme.colors.blue : 'white'};
  color: ${props => props.$isActive ? 'white' : '#666'};
  border-radius: 0.25rem;
  cursor: pointer;

  &:hover {
    background: ${props => props.$isActive ? theme.colors.blue : '#f5f5f5'};
  }
`

const ClaimedStatusStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
`

const SparkleIconStyled = styled(SparkleIcon)`
  width: 1.5rem;
  height: 1.5rem;
  color: ${theme.colors.blue};
  animation: sparkle 0.5s ease-in-out;

  @keyframes sparkle {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
    100% { transform: scale(1) rotate(360deg); }
  }
`

const ClaimedStatusBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
`

const ClaimedTextStyled = styled.span`
  font-size: 0.875rem;
  color: #666;
`

const ButtonStyled = styled.button<{ $isOrange?: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.$isOrange ? '#ff6b35' : '#ddd'};
  background: ${props => props.$isOrange ? '#ff6b35' : 'white'};
  color: ${props => props.$isOrange ? 'white' : '#666'};
  border-radius: 0.25rem;
  cursor: pointer;

  &:hover {
    background: ${props => props.$isOrange ? '#e55a2b' : '#f5f5f5'};
  }

  span {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }
`

const ClockIconStyled = styled(ClockIcon)`
  width: 1rem;
  height: 1rem;
`

const WeekDaysStyled = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
`

const DayStyled = styled.div<{ $isToday: boolean }>`
  padding: 0.5rem;
  text-align: center;
  background: ${props => props.$isToday ? theme.colors.blue : '#f8f9fa'};
  color: ${props => props.$isToday ? 'white' : '#666'};
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: ${props => props.$isToday ? '600' : '400'};
`

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;

  .loading-text {
    color: #636e72;
    font-size: 16px;
    text-align: center;
    margin-top: 1rem;
  }
`

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;

  .error-text {
    color: #d63031;
    font-size: 16px;
    text-align: center;
    margin-bottom: 1rem;
  }

  .retry-button {
    padding: 0.5rem 1rem;
    background: ${theme.colors.primary};
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;

    &:hover {
      background: ${theme.colors.primaryDark};
    }
  }
`