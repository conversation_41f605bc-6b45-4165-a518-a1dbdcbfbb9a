import React, { forwardRef, useEffect, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import Popover from 'react-bootstrap/Popover'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'
import { toast } from 'react-toastify'

import dayjs from 'dayjs'
import moment, { Moment } from 'moment'
import { RootState } from 'store/reducers'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { AvatarInitials } from 'components/ui/AvatarInitials'
import NumberFormatted from 'components/ui/NumberFormatted'
import { OutlineButton } from 'components/ui/OutlineButton'
import CustomSelect from 'components/ui/Select'
import CustomTimePicker from 'components/ui/TimePicker'

import ActivitiesPopover from './ActivitiesPopover'
import DeleteShiftModal from './DeleteShiftModal'
import PayrollToaster from './PayrollToaster'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company } from 'types/company'
import { IEmployee } from 'types/employee'
import { Shift } from 'types/schedule'

import { getEmployeeRate } from 'utils/employees'
import { OverlappingShifts, checkShiftOverlap } from 'utils/attendance'

import {
  analyzeShiftForPopover,
  getPopoverButtonStates,
  type ShiftPopoverAnalysis
} from 'utils/payroll/shiftPopoverAnalysis'
import {
  analyzeShiftStatus,
  type ShiftAnalysis
} from 'utils/payroll/shiftStatusAnalysis'

import closeIcon from 'img/icons/closeIcon.svg'
import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'
import { ReactComponent as WarningIcon } from 'img/icons/exclamationFilled.svg'
import { ReactComponent as PlusIcon } from 'img/icons/plusIcon.svg'
import { ReactComponent as SearchIcon } from 'img/icons/searchIcon.svg'
import { ReactComponent as DeleteIcon } from 'img/icons/trashNewIcon.svg'

// Extended shift type for enhanced properties
type ExtendedAttendanceShift = AttendanceShift & {
  isStartOverlapping?: boolean
  isEndOverlapping?: boolean
  scheduledShift?: Shift
}

// TODO SEVA - once ref is properly send => check TimePicker overflow
type ShiftPopoverProps = {
  onClose: () => void
  showActivityPopoverOnLeft?: boolean
  employee?: IEmployee
  date?: string
  shifts?: AttendanceShift[]
  isNewShift?: boolean
  currentCompany?: Company
  attendanceData?: AttendanceShifts
  onSave?: (newShift: { [key: string]: AttendanceShift }) => void
  onDeleteShift?: (shiftKey: string) => void
}

const ShiftPopover = forwardRef<HTMLDivElement, ShiftPopoverProps>(
  (
    {
      onClose,
      showActivityPopoverOnLeft,
      employee,
      date,
      shifts = [],
      isNewShift = false,
      currentCompany,
      attendanceData,
      onSave,
      onDeleteShift,
      ...rest
    },
    ref
  ) => {
    const [activeShift, setActiveShift] = useState<number | null>(0)

    const isLocaleFr =
      useSelector((state: RootState) => state.i18n.locale) === 'fr'

    // Generate position options matching the exact logic from EmployeeRolesList.js
    const options = React.useMemo(() => {
      if (!currentCompany?.jobs || !employee?.positions) {
        console.log('ShiftPopover: Missing company jobs or employee positions', {
          hasJobs: !!currentCompany?.jobs,
          hasPositions: !!employee?.positions,
          employeePositions: employee?.positions
        })
        return []
      }

      const positionOptions: Array<{ label: string; value: string }> = []

      // Clone and sort jobs by priority (matching EmployeeRolesList.js logic)
      const jobsCopy = { ...currentCompany.jobs }
      const sortedJobs = Object.entries(jobsCopy)
        .filter(([_, value]) => value && !value.archived)
        .map(([key, value]) => ({ ...value, key }))
        .sort((a, b) => a.priority - b.priority)

      // Process each job category
      sortedJobs.forEach((category) => {
        // Check if this employee has this category assigned
        const hasCategory = employee.positions?.some(p => p.categoryId === category.key) || false

        if (hasCategory) {
          // Check if employee has any subcategories for this category
          const employeeSubcategories = employee.positions?.filter(p =>
            p.categoryId === category.key && p.subcategoryId
          ) || []

          if (employeeSubcategories.length > 0) {
            // Employee has specific subcategories - only show those subcategories
            if (category.subcategories) {
              const subcategories = Object.entries(category.subcategories)
                .filter(([_, value]) => value && !value.archived)
                .map(([key, value]) => ({ ...value, key }))
                .sort((a, b) => a.priority - b.priority)

              subcategories.forEach((subcategory) => {
                // Check if this employee has this specific subcategory assigned
                const hasSubcategory = employee.positions?.some(p => p.subcategoryId === subcategory.key) || false

                if (hasSubcategory) {
                  positionOptions.push({
                    label: `${category.name} - ${subcategory.name}`,
                    value: subcategory.key
                  })
                }
              })
            }
          } else {
            // Employee only has the main category (no specific subcategories)
            positionOptions.push({
              label: category.name,
              value: category.key
            })
          }
        }
      })

      console.log('ShiftPopover: Generated role options (matching EmployeeRolesList):', positionOptions)
      return positionOptions
    }, [currentCompany?.jobs, employee?.positions])

    // Get default break duration from company settings
    const getDefaultBreakDuration = React.useCallback(() => {
      if (!currentCompany?.breaks) return 30 // Default fallback

      // Find the most common break duration or the first one
      const breakDurations = Object.values(currentCompany.breaks).map(
        breakSetting => breakSetting.length
      )
      if (breakDurations.length === 0) return 30

      // Return the first break duration found, or 30 as fallback
      return breakDurations[0] || 30
    }, [currentCompany?.breaks])

    const [selectedRole, setSelectedRole] = useState<string | null>(null)

    const [isUnpaidBreak, setIsUnpaidBreak] = useState(false)

    const [roundedTimeStart, setRoundedTimeStart] = useState<Moment | null>(
      null
    )
    const [roundedTimeEnd, setRoundedTimeEnd] = useState<Moment | null>(null)

    const [breakTimeStart, setBreakTimeStart] = useState<Moment | null>(null)
    const [breakTimeEnd, setBreakTimeEnd] = useState<Moment | null>(null)

    // Conflict analysis state
    const [shiftAnalysis, setShiftAnalysis] = useState<ShiftPopoverAnalysis | null>(null)
  const [hoursTableAnalysis, setHoursTableAnalysis] = useState<ShiftAnalysis | null>(null)
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

    // Separate state for each tooltip
    const [showPlannedTooltip, setShowPlannedTooltip] = useState(true)
    const [showClockedTooltip, setShowClockedTooltip] = useState(false)
    // Removed showShortShiftTooltip - shift length validation disabled
    const [showOverlapTooltip, setShowOverlapTooltip] = useState(false)
    const [canShowTooltip, setCanShowTooltip] = useState(false)

    // Separate refs for each tooltip
    const plannedTooltipRef = useRef<HTMLButtonElement>(null)
    const clockedTooltipRef = useRef<HTMLButtonElement>(null)
   // const shortShiftTooltipRef = useRef<HTMLButtonElement>(null)
    const overlapTooltipRef = useRef<HTMLButtonElement>(null)

    //
    const [showActivityPopover, setShowActivityPopover] = useState(false)

    // Get current shift data or create new shift template
    const currentShift: ExtendedAttendanceShift | null = (() => {
      if (activeShift === null) return null

      // If activeShift index exists in shifts array, return existing shift
      if (shifts && activeShift < shifts.length) {
        return shifts[activeShift] as ExtendedAttendanceShift
      }

      // If activeShift equals shifts.length, we're creating a new shift
      if (activeShift === shifts.length) {
        return {
          start: 9 * 60, // 9:00 AM in minutes since midnight
          end: 17 * 60, // 5:00 PM in minutes since midnight
          breaks: {
            'break1': {
              start: 12 * 60, // 12:00 PM
              end: 12 * 60 + getDefaultBreakDuration(),
              lengthRounded: getDefaultBreakDuration()
            }
          },
          positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
          isConfirmed: false,
          manuallyCreated: true
        } as ExtendedAttendanceShift
      }

      return null
    })()

    // Determine shift status based on real data
    const hasWarning = isNewShift
      ? false
      : currentShift
        ? !currentShift.positionId
        : false
    // Check if employee clocked in but didn't clock out using attendance data
    const notClockedOut = React.useMemo(() => {
      if (!attendanceData || !date || !employee || !currentShift) return false

      const dayAttendance = attendanceData[date]
      if (!dayAttendance) return false

      const employeeAttendance = dayAttendance[employee.uid]
      if (!employeeAttendance) return false

      const currentShiftKey = (currentShift as any)?.shiftKey
      if (!currentShiftKey) return false

      const attendanceShift = employeeAttendance[currentShiftKey]
      if (!attendanceShift) return false

      // Employee clocked in but didn't clock out
      return attendanceShift.start && !attendanceShift.end
    }, [attendanceData, date, employee, currentShift])

    // Remove shift duration validation - managers can book any length shifts
    //const isShortShift = false // Disabled as per requirement

    // Use proper checkShiftOverlap function directly (same as PayrollOld)
    const isOverlap = React.useMemo(() => {
      if (!currentShift || !attendanceData || !date || !employee) return false

      // Get all shifts for this employee on this date
      const dayAttendance = attendanceData[date]
      if (!dayAttendance) return false

      const employeeAttendance = dayAttendance[employee.uid]
      if (!employeeAttendance) return false

      // Convert attendance data to format expected by checkShiftOverlap
      const employeeShifts: { [key: string]: AttendanceShift } = {}
      Object.entries(employeeAttendance).forEach(([shiftKey, shift]) => {
        if (shift && typeof shift === 'object' && 'start' in shift) {
          employeeShifts[shiftKey] = shift as AttendanceShift
        }
      })

      // Only check for overlaps if there are multiple shifts
      if (Object.keys(employeeShifts).length < 2) return false

      // Get previous and next day shifts for cross-day overlap detection
      const previousDate = dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')
      const nextDate = dayjs(date).add(1, 'day').format('YYYY-MM-DD')

      const previousDayShifts: { [key: string]: AttendanceShift } = {}
      const nextDayShifts: { [key: string]: AttendanceShift } = {}

      // Get previous day shifts
      const prevDayAttendance = attendanceData[previousDate]?.[employee.uid]
      if (prevDayAttendance) {
        Object.entries(prevDayAttendance).forEach(([shiftKey, shift]) => {
          if (shift && typeof shift === 'object' && 'start' in shift) {
            previousDayShifts[shiftKey] = shift as AttendanceShift
          }
        })
      }

      // Get next day shifts
      const nextDayAttendance = attendanceData[nextDate]?.[employee.uid]
      if (nextDayAttendance) {
        Object.entries(nextDayAttendance).forEach(([shiftKey, shift]) => {
          if (shift && typeof shift === 'object' && 'start' in shift) {
            nextDayShifts[shiftKey] = shift as AttendanceShift
          }
        })
      }

      // Debug: Log input data for checkShiftOverlap
      console.log('📊 ShiftPopover checkShiftOverlap Input:', {
        date,
        employeeId: employee.uid,
        currentShiftKey: (currentShift as any)?.shiftKey,
        employeeShifts: Object.entries(employeeShifts).map(([key, shift]) =>
          `${key}: ${Math.floor((shift.start || 0) / 60)}:${((shift.start || 0) % 60).toString().padStart(2, '0')} - ${Math.floor((shift.end || 0) / 60)}:${((shift.end || 0) % 60).toString().padStart(2, '0')}`
        ),
        previousDayShifts: Object.entries(previousDayShifts).map(([key, shift]) =>
          `${key}: ${Math.floor((shift.start || 0) / 60)}:${((shift.start || 0) % 60).toString().padStart(2, '0')} - ${Math.floor((shift.end || 0) / 60)}:${((shift.end || 0) % 60).toString().padStart(2, '0')}`
        ),
        nextDayShifts: Object.entries(nextDayShifts).map(([key, shift]) =>
          `${key}: ${Math.floor((shift.start || 0) / 60)}:${((shift.start || 0) % 60).toString().padStart(2, '0')} - ${Math.floor((shift.end || 0) / 60)}:${((shift.end || 0) % 60).toString().padStart(2, '0')}`
        )
      })

      // Use the same overlap detection as PayrollOld
      const overlappedShifts = checkShiftOverlap({
        employeeShifts,
        previousDayShifts,
        nextDayShifts
      })

      // Check if current shift has overlaps
      const currentShiftKey = (currentShift as any)?.shiftKey
      if (currentShiftKey && overlappedShifts[currentShiftKey]) {
        const shiftOverlap = overlappedShifts[currentShiftKey]
        return shiftOverlap.isStartOverlapping || shiftOverlap.isEndOverlapping
      }

      return false
    }, [currentShift, attendanceData, date, employee])

    // Debug: Log overlap detection with more detail
    console.log('🔍 ShiftPopover overlap detection (using checkShiftOverlap):', {
      employeeId: employee?.uid,
      date,
      shiftIndex: activeShift,
      isOverlap,
      currentShiftKey: (currentShift as any)?.shiftKey,
      hasCurrentShift: !!currentShift,
      hasAttendanceData: !!attendanceData,
      hoursTableStatus: hoursTableAnalysis?.status,
      hoursTableIssues: hoursTableAnalysis?.issues
    })

    // Initialize selectedRole from current shift data
    useEffect(() => {
      if (currentShift?.positionId) {
        setSelectedRole(currentShift.positionId)
      }
    }, [currentShift?.positionId])

    // Initialize time pickers from current shift data
    useEffect(() => {
      if (currentShift?.start) {
        const startMoment = moment()
          .startOf('day')
          .add(currentShift.start, 'minutes')
        setRoundedTimeStart(startMoment)
      }
      if (currentShift?.end) {
        const endMoment = moment()
          .startOf('day')
          .add(currentShift.end, 'minutes')
        setRoundedTimeEnd(endMoment)
      }
    }, [currentShift?.start, currentShift?.end])

    // Analyze shift for conflicts when shift data changes
    useEffect(() => {
      if (currentShift && employee && date && attendanceData && currentCompany) {
        const analysis = analyzeShiftForPopover(
          currentShift,
          activeShift || 0,
          shifts || [],
          employee.uid,
          date,
          attendanceData,
          currentCompany,
          hasUnsavedChanges
        )
        setShiftAnalysis(analysis)

        // Also run the same analysis as HoursTable for consistency
        const hoursAnalysis = analyzeShiftStatus(
          currentShift,
          employee.uid,
          date,
          attendanceData,
          currentCompany,
          dayjs(date).isSame(dayjs(), 'day')
        )
        setHoursTableAnalysis(hoursAnalysis)

        // Debug: Log both analyses to compare
        console.log('ShiftPopover analysis (popover system):', {
          employeeId: employee.uid,
          date,
          shiftIndex: activeShift,
          overallStatus: analysis.overallStatus,
          conflicts: analysis.conflicts,
          showTooltip: analysis.showTooltip,
          tooltipMessage: analysis.tooltipMessage
        })

        console.log('ShiftPopover analysis (hours table system):', {
          employeeId: employee.uid,
          date,
          shiftIndex: activeShift,
          status: hoursAnalysis.status,
          statusColor: hoursAnalysis.status === 'overlapping' ? 'red' :
                      hoursAnalysis.status === 'problematic' ? 'orange' :
                      hoursAnalysis.status === 'current' ? 'blue' : 'white',
          issues: hoursAnalysis.issues,
          priority: hoursAnalysis.priority
        })

        // Show tooltip if analysis indicates it should be shown
        if (analysis.showTooltip) {
          const timer = setTimeout(() => {
            setCanShowTooltip(true)
            setShowPlannedTooltip(true) // Show the main conflict tooltip
          }, 100)
          return () => clearTimeout(timer)
        }
      }
    }, [currentShift, activeShift, shifts, employee, date, attendanceData, currentCompany, hasUnsavedChanges])

    // Delay showing tooltip to allow parent popover to position itself
    useEffect(() => {
      if (hasWarning) {
        const timer = setTimeout(() => {
          setCanShowTooltip(true)
        }, 100) // Small delay to ensure parent popover is positioned

        return () => clearTimeout(timer)
      }
    }, [hasWarning])

    const handleClosePopover = () => {
      onClose()
      setShowPlannedTooltip(false)
      setShowClockedTooltip(false)
      // Removed setShowShortShiftTooltip - shift length validation disabled
      setShowOverlapTooltip(false)
    }

    const activityPopoverRef = useRef<HTMLDivElement>(null)
    const [showDeleteShiftModal, setShowDeleteShiftModal] = useState(false)

    const canReclaim = !isNewShift && currentShift && currentShift.positionId
    const saveAmount = 5.4
    const [hasClaimed, setHasClaimed] = useState(false)

    const onChangeStatus = async (
      status: 'approve' | 'delete' | 'claim' | 'modify',
      onUndo?: () => void
    ) => {
      console.log('🔄 onChangeStatus called:', {
        status,
        isNewShift,
        activeShift,
        hasCurrentShift: !!currentShift,
        hasOnSave: !!onSave,
        hasEmployee: !!employee,
        hasDate: !!date,
        shiftsLength: shifts?.length
      })

      if (!currentShift || !onSave || !employee || !date) {
        console.error('❌ Missing required data:', {
          currentShift: !!currentShift,
          onSave: !!onSave,
          employee: !!employee,
          date: !!date
        })
        toast.error('Missing required data to update shift status')
        return
      }

      try {
        if (status === 'approve') {
          // Mark shift as confirmed/approved
          const approvedShift = {
            ...currentShift,
            isConfirmed: true,
            positionId: selectedRole || currentShift.positionId,
            // Update break times from UI if they were modified
            breaks: breakTimeStart && breakTimeEnd ? {
              'break1': {
                start: breakTimeStart.hour() * 60 + breakTimeStart.minute(),
                end: breakTimeEnd.hour() * 60 + breakTimeEnd.minute(),
                lengthRounded: Math.max(0,
                  (breakTimeEnd.hour() * 60 + breakTimeEnd.minute()) -
                  (breakTimeStart.hour() * 60 + breakTimeStart.minute())
                )
              }
            } : currentShift.breaks
          }

          // For existing shifts, use the shiftKey from the shifts array
          // For new shifts, generate a new key
          let shiftKey: string
          if (isNewShift) {
            shiftKey = `shift_${Date.now()}`
          } else {
            // Get the shiftKey from the shifts array using the activeShift index
            if (activeShift === null || activeShift >= shifts.length) {
              console.error('Invalid activeShift index:', { activeShift, shiftsLength: shifts.length })
              toast.error('Unable to save shift: invalid shift index')
              return
            }

            const shiftWithKey = shifts[activeShift]
            shiftKey = (shiftWithKey as any)?.shiftKey

            if (!shiftKey) {
              console.error('No shiftKey found for existing shift:', { activeShift, shift: shiftWithKey })
              toast.error('Unable to save shift: missing shift key')
              return
            }
          }

          console.log('💾 Saving shift with key:', { shiftKey, isNewShift, activeShift, approvedShift })
          await onSave({ [shiftKey]: approvedShift })
        }

        // Show success toast
        toast(
          <PayrollToaster
            onUndo={onUndo}
            status={status}
          />,
          {
            className: 'payroll-toaster',
            closeButton: false,
            hideProgressBar: true,
            position: 'top-right',
            autoClose: 5000,
            pauseOnFocusLoss: false
          }
        )
      } catch (error) {
        toast.error('Failed to update shift status')
        console.error('Error updating shift status:', error)
      }
    }

    // Save shift data with updated role
    const handleSaveShift = async () => {
      if (!selectedRole || !onSave || !employee) {
        toast.error('Please select a role before saving')
        return
      }

      let shiftData: { [key: string]: AttendanceShift }

      // Check if we're creating a new shift (activeShift equals shifts.length)
      const isCreatingNewShift = activeShift === shifts.length || isNewShift || !currentShift

      if (isCreatingNewShift) {
        // Create new shift
        const newShiftKey = `shift_${Date.now()}`
        const newShift: AttendanceShift = {
          positionId: selectedRole,
          manuallyCreated: true,
          isConfirmed: false,
          breaks: {},
          start: roundedTimeStart
            ? roundedTimeStart.hour() * 60 + roundedTimeStart.minute()
            : undefined,
          end: roundedTimeEnd
            ? roundedTimeEnd.hour() * 60 + roundedTimeEnd.minute()
            : undefined
        }

        shiftData = {
          [newShiftKey]: newShift
        }
      } else {
        // Update existing shift
        const { shiftKey, ...shiftWithoutKey } = currentShift as any

        const updatedShift = {
          ...shiftWithoutKey,
          positionId: selectedRole
        }

        shiftData = {
          [shiftKey || `shift_${Date.now()}`]: updatedShift
        }
      }

      try {
        onSave(shiftData)
        toast.success(
          isCreatingNewShift
            ? 'Shift created successfully!'
            : 'Shift updated successfully!'
        )
        onClose()
      } catch (error) {
        toast.error(
          isNewShift ? 'Failed to create shift' : 'Failed to update shift'
        )
        console.error('Error saving shift:', error)
      }
    }

    // Get employee rate based on selected role
    // Note: Employee rates are stored separately in EmployeeRates/{companyId}/{employeeId}/{positionId}
    // For now, we'll use the position's default rate from company jobs
    const { rate, type, additionalSalary } = getEmployeeRate({
      employeeRate: {}, // Would need to fetch from EmployeeRates in real implementation
      positionId: selectedRole || '',
      jobs: currentCompany?.jobs || {}
    })

    const numericRate = Number(rate) || 0
    const formattedSalary = isLocaleFr
      ? numericRate.toLocaleString('fr-FR', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
      : numericRate.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })

    // Display rate type and bonus information
    const hasBonus = additionalSalary && additionalSalary > 0
    const bonusAmount = Number(additionalSalary) || 0

    // Handle case when employee data is not available
    // TODO Lie - udate with proper styles, translations if this block is needed
    if (!employee || !date) {
      return (
        <PopoverStyled
          {...rest}
          id='payroll_shift-popover'
          ref={ref}
        >
          <ContainerStyled>
            <HeaderStyled>
              <HeaderTitleStyled>Shift Details</HeaderTitleStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img
                  src={closeIcon}
                  alt=''
                />
              </CloseButtonStyled>
            </HeaderStyled>
            <div style={{ padding: '1rem', textAlign: 'center' }}>
              <p>Unable to load shift data.</p>
              <p
                style={{
                  fontSize: '0.8rem',
                  color: '#666',
                  marginTop: '0.5rem'
                }}
              >
                Employee:{' '}
                {employee
                  ? `${employee.name} ${employee.surname}`
                  : 'Not found'}
              </p>
              <p style={{ fontSize: '0.8rem', color: '#666' }}>
                Date: {date || 'Not provided'}
              </p>
            </div>
          </ContainerStyled>
        </PopoverStyled>
      )
    }
    return (
      <>
        <PopoverStyled
          {...rest}
          id='payroll_shift-popover'
          ref={ref}
        >
          {showDeleteShiftModal && (
            <DeleteShiftModal
              onClose={() => setShowDeleteShiftModal(false)}
              onDelete={() => {
                onChangeStatus('delete')
                setShowDeleteShiftModal(false)
                onClose()
              }}
            />
          )}
          <ContainerStyled ref={activityPopoverRef}>
            <HeaderStyled>
              <HeaderTitleStyled>
                {dayjs(date).format('dddd D')}
                {/* TODO Lie - what is is this? Lie: Keep it for now for future use, incase we receive the date in wrong format*/}
                {/* Debug: log the date prop and parsed date */}
                {/* {(() => {
                  // Defensive: parse date as local, not UTC, and avoid off-by-one
                  // Accepts ISO string or yyyy-mm-dd
                  let parsedDate: Date | null = null
                  if (typeof date === 'string') {
                    // If date is yyyy-mm-dd, treat as local
                    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                      const [y, m, d] = date.split('-').map(Number)
                      parsedDate = new Date(y, m - 1, d)
                    } else {
                      // Otherwise, let Date parse it
                      parsedDate = new Date(date)
                    }
                  }
                  // Fallback: show raw date if parsing fails
                  if (!parsedDate || isNaN(parsedDate.getTime())) {
                    return date || 'Invalid date'
                  }
                  // Debug log
                  if (process.env.NODE_ENV !== 'production') {
                    // eslint-disable-next-line no-console
                    console.log(
                      'ShiftPopover date prop:',
                      date,
                      'parsed:',
                      parsedDate
                    )
                  }
                  return parsedDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    day: 'numeric'
                  })
                })()} */}
              </HeaderTitleStyled>
              <ActivityButtonStyled
                onClick={() => setShowActivityPopover(!showActivityPopover)}
              >
                <SearchIconStyled />
                {I18n.t('payroll.activities')}
              </ActivityButtonStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img
                  src={closeIcon}
                  alt=''
                />
              </CloseButtonStyled>
            </HeaderStyled>
            <InfoBlockStyled>
              <AvatarInitialsStyled employee={employee} />
              <InfoBlockTextStyled>
                {employee.name} {employee.surname}
                <span style={{ fontSize: '0.8rem', color: '#666', marginLeft: '0.5rem' }}>
                  #{employee?.payrollId || employee?.customId || 'N/A'}
                </span>
              </InfoBlockTextStyled>
            </InfoBlockStyled>
            <ShiftListStyled>
              {/* Render existing shifts (Shift 1, Shift 2, etc.) */}
              {shifts.map((shift, index) => {
                // Analyze each shift for conflicts to show indicators
                const shiftAnalysisForTab = employee && date && attendanceData && currentCompany
                  ? analyzeShiftForPopover(
                      shift,
                      index,
                      shifts || [],
                      employee.uid,
                      date,
                      attendanceData,
                      currentCompany,
                      false
                    )
                  : null

                return (
                  <ShiftListItemStyled
                    key={index}
                    $isActive={activeShift === index}
                    $conflictType={shiftAnalysisForTab?.overallStatus}
                    onClick={() => setActiveShift(index)}
                  >
                    {I18n.t('common.shift')} {index + 1}
                    {shiftAnalysisForTab?.overallStatus === 'red' && (
                      <ConflictIndicatorStyled $type="red">
                        <WarningIconStyled />
                      </ConflictIndicatorStyled>
                    )}
                    {shiftAnalysisForTab?.overallStatus === 'orange' && (
                      <ConflictIndicatorStyled $type="orange">
                        <WarningIconStyled />
                      </ConflictIndicatorStyled>
                    )}
                  </ShiftListItemStyled>
                )
              })}

              {/* Show "+" tab if less than 4 shifts exist */}
              {shifts.length < 4 && (
                <ShiftListItemStyled
                  $isActive={activeShift === shifts.length}
                  onClick={() => {
                    // When clicking "+", only switch to new shift tab (don't create shift yet)
                    const newShiftIndex = shifts.length
                    setActiveShift(newShiftIndex)
                    // Shift will be created when user saves/approves
                  }}
                >
                  +
                </ShiftListItemStyled>
              )}
            </ShiftListStyled>
            <RoleBlockStyled>
              <RoleBlockLabelStyled>
                {I18n.t('common.role')}
              </RoleBlockLabelStyled>
              <CustomSelectStyled
                options={options}
                value={options.find(option => option.value === selectedRole)}
                onChange={option =>
                  option && setSelectedRole(option.value as string)
                }
                placeholder={I18n.t('payroll.select_role')}
                noOptionsMessage={I18n.t('payroll.no_roles_available')}
                components={{
                  IndicatorSeparator: null
                }}
                $noValue={!selectedRole}
              />
              <RoleBlockSalaryStyled>
                {/* Display rate based on type */}
                {type === 'hourly' && (
                  <div>{formattedSalary}/hour</div>
                )}
                {type === 'yearly' && (
                  <div>
                    <div>{formattedSalary}/year</div>
                    <div style={{ fontSize: '0.75rem', color: '#666', marginTop: '2px' }}>
                      ({(numericRate / 365).toLocaleString(isLocaleFr ? 'fr-FR' : 'en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}/day)
                    </div>
                  </div>
                )}

                {/* Show additional salary (bonus per shift) if available */}
                {hasBonus && (
                  <div style={{ fontSize: '0.75rem', backgroundColor: '#a4cbb0', marginTop: '2px' }}>
                    + {bonusAmount.toLocaleString(isLocaleFr ? 'fr-FR' : 'en-US', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}
                  </div>
                )}
              </RoleBlockSalaryStyled>
            </RoleBlockStyled>
            <DividerStyled />
            <ScrollBlockStyled>
              <ShiftBlockStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('common.shift')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.total')}</RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.planned')}</RowCellStyled>
                  <RowCellStyled>
                    {currentShift?.scheduledShift?.start
                      ? `${Math.floor(currentShift.scheduledShift.start / 60)}:${(currentShift.scheduledShift.start % 60).toString().padStart(2, '0')}`
                      : '--:--'}
                  </RowCellStyled>
                  <RowCellStyled>
                    {currentShift?.scheduledShift?.end
                      ? `${Math.floor(currentShift.scheduledShift.end / 60)}:${(currentShift.scheduledShift.end % 60).toString().padStart(2, '0')}`
                      : '--:--'}
                  </RowCellStyled>
                  <RowCellStyled>
                    {(hasWarning || (shiftAnalysis && shiftAnalysis.showTooltip)) && (
                      <WarningTooltipButton
                        isRed={shiftAnalysis?.overallStatus === 'red'}
                        tooltipText={
                          shiftAnalysis?.tooltipMessage ||
                          I18n.t('payroll.shift_not_planned_for_employee')
                        }
                        show={showPlannedTooltip}
                        canShow={canShowTooltip}
                        onToggleTooltip={() =>
                          setShowPlannedTooltip(!showPlannedTooltip)
                        }
                        onHide={() => setShowPlannedTooltip(false)}
                        buttonRef={plannedTooltipRef}
                      />
                    )}
                  </RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.clocked')}</RowCellStyled>
                  <RowCellStyled>
                    {(() => {
                      // Get actual clock-in time from attendance data
                      if (!attendanceData || !date || !employee) return '--:--'

                      const dayAttendance = attendanceData[date]
                      if (!dayAttendance) return '--:--'

                      const employeeAttendance = dayAttendance[employee.uid]
                      if (!employeeAttendance) return '--:--'

                      // Find the current shift's attendance data
                      const currentShiftKey = (currentShift as any)?.shiftKey
                      if (!currentShiftKey) return '--:--'

                      const attendanceShift = employeeAttendance[currentShiftKey]
                      if (!attendanceShift || !attendanceShift.start) return '--:--'

                      const clockInMinutes = attendanceShift.start
                      return `${Math.floor(clockInMinutes / 60)}:${(clockInMinutes % 60).toString().padStart(2, '0')}`
                    })()}
                  </RowCellStyled>
                  <RowCellStyled>
                    {(() => {
                      // Get actual clock-out time from attendance data
                      if (!attendanceData || !date || !employee) return '--:--'

                      const dayAttendance = attendanceData[date]
                      if (!dayAttendance) return '--:--'

                      const employeeAttendance = dayAttendance[employee.uid]
                      if (!employeeAttendance) return '--:--'

                      // Find the current shift's attendance data
                      const currentShiftKey = (currentShift as any)?.shiftKey
                      if (!currentShiftKey) return '--:--'

                      const attendanceShift = employeeAttendance[currentShiftKey]
                      if (!attendanceShift || !attendanceShift.end) return '--:--'

                      const clockOutMinutes = attendanceShift.end
                      return `${Math.floor(clockOutMinutes / 60)}:${(clockOutMinutes % 60).toString().padStart(2, '0')}`
                    })()}
                  </RowCellStyled>
                  <RowCellStyled>
                    {notClockedOut && (
                      <WarningTooltipButton
                        tooltipText={I18n.t(
                          'payroll.employee_did_not_clock_out'
                        )}
                        show={showClockedTooltip}
                        canShow={canShowTooltip}
                        onToggleTooltip={() =>
                          setShowClockedTooltip(!showClockedTooltip)
                        }
                        onHide={() => setShowClockedTooltip(false)}
                        buttonRef={clockedTooltipRef}
                      />
                    )}
                  </RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.rounded')}</RowCellStyled>
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeStart(value)
                    }}
                    value={roundedTimeStart}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeStart}
                    isFixedMenu
                  />
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeEnd(value)
                    }}
                    value={roundedTimeEnd}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeEnd}
                    isFixedMenu
                  />
                  <RowCellStyled>
                    {/* Removed short shift validation - managers can book any length shifts */}
                    {(() => {
                      // Debug overlap warning rendering
                      console.log('🚨 Overlap warning render check:', {
                        isOverlap,
                        hoursTableStatus: hoursTableAnalysis?.status,
                        issues: hoursTableAnalysis?.issues,
                        showOverlapTooltip,
                        canShowTooltip,
                        willRenderWarning: !!isOverlap
                      })

                      return isOverlap && (
                        <WarningTooltipButton
                          isRed
                          tooltipText={
                            hoursTableAnalysis?.issues?.join(', ') ||
                            'Shift overlaps detected - please resolve conflicts'
                          }
                          show={showOverlapTooltip}
                          canShow={canShowTooltip}
                          onToggleTooltip={() =>
                            setShowOverlapTooltip(!showOverlapTooltip)
                          }
                          onHide={() => setShowOverlapTooltip(false)}
                          buttonRef={overlapTooltipRef}
                        />
                      )
                    })()}
                    {(() => {
                      // Calculate actual hours based on start and end time
                      // Use currentShift from the logic above (handles both existing and new shifts)
                      if (!currentShift || !currentShift.start) return '0'

                      // Convert timestamps to minutes since midnight
                      const startMinutes = currentShift.start
                      let endMinutes = currentShift.end

                      // If no end time (not clocked out), use default 6.5 hours
                      if (!endMinutes) {
                        endMinutes = startMinutes + (6.5 * 60) // 6.5 hours in minutes
                      }

                      // Calculate total minutes worked
                      let totalMinutes = endMinutes - startMinutes

                      // Handle overnight shifts (end time is next day)
                      if (totalMinutes < 0) {
                        totalMinutes += 24 * 60 // Add 24 hours in minutes
                      }

                      // Subtract break time
                      const breakMinutes = Object.values(currentShift.breaks || {}).reduce((total, breakItem) => {
                        return total + (breakItem.lengthRounded || 0)
                      }, 0)

                      const workMinutes = totalMinutes - breakMinutes
                      const workHours = workMinutes / 60

                      return Math.max(0, workHours).toFixed(1)
                    })()}{I18n.t('common.hours_shorten').toLowerCase()}
                    {/* Reset Button */}
                    <DeleteButtonStyled
                      onClick={() => {
                        // Reset shift to default values
                        if (currentShift && onSave && employee && date) {
                          const defaultShift: AttendanceShift = {
                            start: 9 * 60, // 9:00 AM
                            end: 17 * 60, // 5:00 PM
                            breaks: {
                              'break1': {
                                start: 12 * 60, // 12:00 PM
                                end: 12 * 60 + getDefaultBreakDuration(),
                                lengthRounded: getDefaultBreakDuration()
                              }
                            },
                            positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
                            isConfirmed: false,
                            manuallyCreated: true
                          }

                          const shiftKey = (currentShift as any).shiftKey || `shift_${Date.now()}`
                          onSave({ [shiftKey]: defaultShift })
                        }
                      }}
                      title="Reset to default"
                    >
                    </DeleteButtonStyled>

                    {/* Delete Button */}
                    <DeleteButtonStyled
                      onClick={() => {
                        setShowDeleteShiftModal(true)
                        setShowClockedTooltip(false)
                        // Removed setShowShortShiftTooltip - shift length validation disabled
                        setShowOverlapTooltip(false)
                        setShowPlannedTooltip(false)
                        setCanShowTooltip(false)
                      }}
                    >
                      <DeleteIconStyled />
                    </DeleteButtonStyled>
                  </RowCellStyled>
                </RowStyled>
              </ShiftBlockStyled>
              <DividerStyled />

              <BreakBlockStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.break')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.total')}</RowCellStyled>
                </RowStyled>

                {/* Render multiple break rows dynamically */}
                {(() => {
                  const breaks = currentShift?.breaks || {}
                  const breakEntries = Object.entries(breaks)

                  // If no breaks exist, show one empty break row
                  if (breakEntries.length === 0) {
                    return (
                      <RowStyled key="empty-break">
                        <RowCellStyled>
                          {isUnpaidBreak
                            ? I18n.t('payroll.paid')
                            : I18n.t('payroll.unpaid')}
                          <CustomCheckboxStyled
                            $isActive={isUnpaidBreak}
                            onClick={() => setIsUnpaidBreak(!isUnpaidBreak)}
                          />
                        </RowCellStyled>
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => setBreakTimeStart(value)}
                          value={breakTimeStart}
                          placeholder='-:-'
                          hideArrow
                          $noValue={!breakTimeStart}
                          isFixedMenu
                        />
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => setBreakTimeEnd(value)}
                          value={breakTimeEnd}
                          placeholder='-:-'
                          hideArrow
                          $noValue={!breakTimeEnd}
                          isFixedMenu
                        />
                        <RowCellStyled>
                          {getDefaultBreakDuration()}
                          {I18n.t('common.minutes_shorten').toLowerCase()}
                          <DeleteButtonStyled
                            onClick={() => {
                              // Clear values only (don't delete since it's the only one)
                              setBreakTimeStart(null)
                              setBreakTimeEnd(null)
                            }}
                          >
                            <DeleteIconStyled />
                          </DeleteButtonStyled>
                        </RowCellStyled>
                      </RowStyled>
                    )
                  }

                  // Render existing breaks
                  return breakEntries.map(([breakId, breakData], index) => (
                    <RowStyled key={breakId}>
                      <RowCellStyled>
                        {isUnpaidBreak
                          ? I18n.t('payroll.paid')
                          : I18n.t('payroll.unpaid')}
                        <CustomCheckboxStyled
                          $isActive={isUnpaidBreak}
                          onClick={() => setIsUnpaidBreak(!isUnpaidBreak)}
                        />
                      </RowCellStyled>
                      <CustomTimePickerStyled
                        minuteStep={1}
                        onChange={value => {
                          if (currentShift && onSave && employee && date) {
                            const startMinutes = value ? value.hour() * 60 + value.minute() : breakData.start
                            const updatedShift = {
                              ...currentShift,
                              breaks: {
                                ...currentShift.breaks,
                                [breakId]: {
                                  ...breakData,
                                  start: startMinutes
                                }
                              }
                            }
                            const shiftKey = (currentShift as any).shiftKey || `shift_${Date.now()}`
                            onSave({ [shiftKey]: updatedShift })
                          }
                        }}
                        value={breakData.start ? moment().hour(Math.floor(breakData.start / 60)).minute(breakData.start % 60) : null}
                        placeholder='-:-'
                        hideArrow
                        $noValue={!breakData.start}
                        isFixedMenu
                      />
                      <CustomTimePickerStyled
                        minuteStep={1}
                        onChange={value => {
                          if (currentShift && onSave && employee && date) {
                            const endMinutes = value ? value.hour() * 60 + value.minute() : (breakData.end || 0)
                            const startMinutes = breakData.start || 0
                            const updatedShift = {
                              ...currentShift,
                              breaks: {
                                ...currentShift.breaks,
                                [breakId]: {
                                  ...breakData,
                                  end: endMinutes,
                                  lengthRounded: Math.max(0, endMinutes - startMinutes)
                                }
                              }
                            }
                            const shiftKey = (currentShift as any).shiftKey || `shift_${Date.now()}`
                            onSave({ [shiftKey]: updatedShift })
                          }
                        }}
                        value={breakData.end ? moment().hour(Math.floor(breakData.end / 60)).minute(breakData.end % 60) : null}
                        placeholder='-:-'
                        hideArrow
                        $noValue={!breakData.end}
                        isFixedMenu
                      />
                      <RowCellStyled>
                        {breakData.lengthRounded || 0}
                        {I18n.t('common.minutes_shorten').toLowerCase()}
                        <DeleteButtonStyled
                          onClick={() => {
                            if (currentShift && onSave && employee && date) {
                              const breakCount = Object.keys(currentShift.breaks || {}).length

                              if (breakCount > 1) {
                                // Delete this specific break if more than one exists
                                const updatedBreaks = { ...currentShift.breaks }
                                delete updatedBreaks[breakId]

                                const updatedShift = {
                                  ...currentShift,
                                  breaks: updatedBreaks
                                }
                                const shiftKey = (currentShift as any).shiftKey || `shift_${Date.now()}`
                                onSave({ [shiftKey]: updatedShift })
                              } else {
                                // Clear values only if it's the last break
                                const updatedShift = {
                                  ...currentShift,
                                  breaks: {
                                    [breakId]: {
                                      start: 0,
                                      end: 0,
                                      lengthRounded: 0
                                    }
                                  }
                                }
                                const shiftKey = (currentShift as any).shiftKey || `shift_${Date.now()}`
                                onSave({ [shiftKey]: updatedShift })
                              }
                            }
                          }}
                        >
                          <DeleteIconStyled />
                        </DeleteButtonStyled>
                      </RowCellStyled>
                    </RowStyled>
                  ))
                })()}

                <AddBreakButtonStyled
                  onClick={() => {
                    // Add a new break row
                    if (currentShift && onSave && employee && date) {
                      const newBreakId = `break_${Date.now()}`
                      const defaultBreakStart = 12 * 60 // 12:00 PM
                      const defaultBreakEnd = defaultBreakStart + getDefaultBreakDuration()

                      const updatedShift = {
                        ...currentShift,
                        breaks: {
                          ...currentShift.breaks,
                          [newBreakId]: {
                            start: defaultBreakStart,
                            end: defaultBreakEnd,
                            lengthRounded: getDefaultBreakDuration()
                          }
                        }
                      }

                      const shiftKey = (currentShift as any).shiftKey || `shift_${Date.now()}`
                      onSave({ [shiftKey]: updatedShift })
                    }
                  }}
                >
                  {I18n.t('payroll.add_break')} <PlusIconStyled />
                </AddBreakButtonStyled>
              </BreakBlockStyled>
            </ScrollBlockStyled>

            <DividerStyled />

            <TotalBlockStyled>
              <TotalBlockLabelStyled>
                {I18n.t('payroll.total')}
              </TotalBlockLabelStyled>
              <TotalBlockValueStyled>
                {(() => {
                  // Calculate total hours including current shift being edited
                  let totalHours = 0

                  // Add hours from existing shifts
                  shifts.forEach((shift, index) => {
                    // Skip the current shift if we're editing it
                    if (index === activeShift) return

                    if (!shift.start || !shift.end) return

                    let shiftMinutes = shift.end - shift.start
                    if (shiftMinutes < 0) shiftMinutes += 24 * 60

                    const breakMinutes = Object.values(shift.breaks || {}).reduce((breakTotal, breakItem) => {
                      return breakTotal + (breakItem.lengthRounded || 0)
                    }, 0)

                    const workMinutes = shiftMinutes - breakMinutes
                    totalHours += workMinutes / 60
                  })

                  // Add hours from current shift being edited
                  if (currentShift && currentShift.start) {
                    const startMinutes = currentShift.start
                    let endMinutes = currentShift.end || (startMinutes + 6.5 * 60) // Default 6.5 hours

                    let shiftMinutes = endMinutes - startMinutes
                    if (shiftMinutes < 0) shiftMinutes += 24 * 60

                    // Calculate break minutes from UI inputs or shift data
                    let breakMinutes = 0
                    if (breakTimeStart && breakTimeEnd) {
                      const breakStart = breakTimeStart.hour() * 60 + breakTimeStart.minute()
                      const breakEnd = breakTimeEnd.hour() * 60 + breakTimeEnd.minute()
                      breakMinutes = Math.max(0, breakEnd - breakStart)
                    } else {
                      breakMinutes = Object.values(currentShift.breaks || {}).reduce((breakTotal, breakItem) => {
                        return breakTotal + (breakItem.lengthRounded || 0)
                      }, 0)
                    }

                    const workMinutes = shiftMinutes - breakMinutes
                    totalHours += workMinutes / 60
                  }

                  return Math.max(0, totalHours).toFixed(2)
                })()}{I18n.t('common.hours_shorten').toLowerCase()}
              </TotalBlockValueStyled>
              <TotalBlockValueStyled>
                <NumberFormatted value={(() => {
                  // Calculate total pay based on hours and rate type
                  const totalHours = shifts.reduce((total, shift) => {
                    if (!shift.start || !shift.end) return total

                    let shiftMinutes = shift.end - shift.start
                    if (shiftMinutes < 0) shiftMinutes += 24 * 60

                    const breakMinutes = Object.values(shift.breaks || {}).reduce((breakTotal, breakItem) => {
                      return breakTotal + (breakItem.lengthRounded || 0)
                    }, 0)

                    const workMinutes = shiftMinutes - breakMinutes
                    return total + (workMinutes / 60)
                  }, 0)

                  const numericRate = Number(rate) || 0

                  if (type === 'hourly') {
                    // For hourly employees: hours * hourly rate
                    return Math.max(0, totalHours * numericRate)
                  } else {
                    // For yearly salary employees: yearly salary / 365 (daily rate)
                    return Math.max(0, numericRate / 365)
                  }
                })()} />
              </TotalBlockValueStyled>
            </TotalBlockStyled>

            <ButtonBlockStyled>
              {shiftAnalysis && (() => {
                const buttonStates = getPopoverButtonStates(shiftAnalysis)

                return (
                  <>
                    <ButtonWrapStyled>
                      {buttonStates.showReclaim && (
                        <OverlayTrigger
                          trigger={['hover', 'focus']}
                          placement='top-start'
                          overlay={
                            <TooltipStyled
                              id='shift-popover_reclaim-tooltip'
                              $isReclaimTooltip
                            >
                              {I18n.t('payroll.you_save')} ${saveAmount}{' '}
                              {I18n.t('payroll.with_planned_start_time')}
                            </TooltipStyled>
                          }
                        >
                          <OrangeButtonStyled
                            color='orange'
                            onClick={() => {
                              onChangeStatus('claim', () =>
                                setHasClaimed(!hasClaimed)
                              )
                              setHasClaimed(!hasClaimed)
                              setHasUnsavedChanges(true)
                            }}
                            $isActive={hasClaimed}
                          >
                            <p>
                              {I18n.t('payroll.reclaim')}{' '}
                              <NumberFormatted value={saveAmount} />
                            </p>
                            <p>
                              {I18n.t('payroll.saved')}{' '}
                              <NumberFormatted value={saveAmount} />
                            </p>
                          </OrangeButtonStyled>
                        </OverlayTrigger>
                      )}
                    </ButtonWrapStyled>
                    <ButtonWrapStyled>
                      {/* Always show approve button - remove blocking conditions */}
                      <SaveButtonStyled
                        color={buttonStates.approveButtonColor || 'green'}
                        onClick={() => {
                          if (hasClaimed) {
                            setHasClaimed(!hasClaimed)
                          } else {
                            onChangeStatus('approve')
                            onClose()
                          }
                        }}
                        $isActive={hasClaimed}
                        $conflictColor={shiftAnalysis.overallStatus}
                      >
                        <span>{I18n.t('common.approve')}</span>
                        <span>{I18n.t('common.undo')}</span>
                      </SaveButtonStyled>
                    </ButtonWrapStyled>
                  </>
                )
              })()}

              {/* Fallback for when analysis is not available */}
              {!shiftAnalysis && (
                <>
                  <ButtonWrapStyled>
                    {canReclaim && (
                      <OverlayTrigger
                        trigger={['hover', 'focus']}
                        placement='top-start'
                        overlay={
                          <TooltipStyled
                            id='shift-popover_reclaim-tooltip'
                            $isReclaimTooltip
                          >
                            {I18n.t('payroll.you_save')} ${saveAmount}{' '}
                            {I18n.t('payroll.with_planned_start_time')}
                          </TooltipStyled>
                        }
                      >
                        <OrangeButtonStyled
                          color='orange'
                          onClick={ () =>
                          {
                            if ( hasClaimed )
                            {
                              setHasClaimed( !hasClaimed );
                            } else
                            {
                              onChangeStatus( 'approve' );
                              onClose();
                            }
                          } }
                          $isActive={hasClaimed}
                        >
                          <p>
                            {I18n.t('payroll.reclaim')}{' '}
                            <NumberFormatted value={saveAmount} />
                          </p>
                          <p>
                            {I18n.t('payroll.saved')}{' '}
                            <NumberFormatted value={saveAmount} />
                          </p>
                        </OrangeButtonStyled>
                      </OverlayTrigger>
                    )}
                  </ButtonWrapStyled>
                  <ButtonWrapStyled>
                    {canReclaim ? (
                      <SaveButtonStyled
                        color='green'
                        onClick={() => {
                          if (hasClaimed) {
                            setHasClaimed(!hasClaimed)
                          } else {
                            onChangeStatus('approve')
                            onClose()
                          }
                        }}
                        $isActive={hasClaimed}
                      >
                        <span>{I18n.t('common.approve')}</span>
                        <span>{I18n.t('common.undo')}</span>
                      </SaveButtonStyled>
                    ) : (
                      <GreyButtonStyled onClick={handleSaveShift}>
                        {I18n.t('common.save')}
                      </GreyButtonStyled>
                    )}
                  </ButtonWrapStyled>
                </>
              )}
            </ButtonBlockStyled>
          </ContainerStyled>
        </PopoverStyled>
        <Overlay
          rootClose
          show={showActivityPopover}
          placement={showActivityPopoverOnLeft ? 'left-start' : 'right-start'}
          target={() => activityPopoverRef.current}
          onHide={() => setShowActivityPopover(false)}
        >
          <ActivitiesPopover
            onClose={() => setShowActivityPopover(false)}
            employee={employee}
            date={date}
            currentCompany={currentCompany}
          />
        </Overlay>
      </>
    )
  }
)

type WarningTooltipButtonProps = {
  isRed?: boolean
  tooltipText: string
  show: boolean
  canShow: boolean
  onToggleTooltip: () => void
  onHide: () => void
  buttonRef: React.RefObject<HTMLButtonElement>
}

const WarningTooltipButton = ({
  isRed,
  tooltipText,
  show,
  canShow,
  onToggleTooltip,
  onHide,
  buttonRef
}: WarningTooltipButtonProps) => (
  <>
    <WarningButtonStyled
      $isRed={isRed}
      onClick={() => canShow && onToggleTooltip()}
      ref={buttonRef}
    >
      <WarningIconStyled />
    </WarningButtonStyled>
    <Overlay
      show={show && canShow}
      placement='top'
      target={() => buttonRef.current}
      onHide={onHide}
    >
      <TooltipStyled
        $isRed={isRed}
        onClick={(e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation()}
        id='payroll_shift-popover-tooltip'
      >
        {tooltipText}
        <CloseButtonTooltipStyled onClick={onHide}>
          <CloseIconStyled />
        </CloseButtonTooltipStyled>
      </TooltipStyled>
    </Overlay>
  </>
)

export default ShiftPopover

const PopoverStyled = styled(Popover)`
  width: 21rem;
  max-width: 21rem;
  /* SEVA: REMOVE THIS */
  margin-right: -10rem !important;

  border: 0;
  border-radius: 0.8rem;

  .arrow {
    display: none;
  }
`

const ContainerStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.8rem;
  padding: 0.8rem 1.2rem;
`

const HeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  gap: 0.5rem;
`

const HeaderTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
`

const ActivityButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.2rem;
  padding: 0.1rem 0.8rem;
  margin-left: auto;

  border: none;
  background: none;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  opacity: 0.8;
  :hover,
  :focus {
    opacity: 1;
  }
`
const SearchIconStyled = styled(SearchIcon)`
  width: 0.75rem;
  height: 0.75rem;
  fill: currentColor;
`

const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;

  border: none;
  opacity: 0.5;
  background: none;
  img {
    width: 0.9rem;
    height: 0.9rem;
  }
  :hover,
  :focus {
    opacity: 1;
  }
`

const InfoBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.4rem;
`

const AvatarInitialsStyled = styled(AvatarInitials)`
  width: 1.6rem;
  height: 1.6rem;
  font-size: 0.8rem;
`

const InfoBlockTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

const ShiftListStyled = styled.div`
  display: flex;
  align-items: center;

  border-bottom: 1px solid #d7dfe9;
`

const ShiftListItemStyled = styled.button<{
  $isActive?: boolean
  $conflictType?: 'orange' | 'red' | 'grey' | 'green'
}>`
  display: flex;
  align-self: stretch;
  align-items: center;
  justify-content: space-between;

  padding: 0.2rem 0.4rem;

  position: relative;

  border: none;
  background: none;

  color: ${({ $isActive, $conflictType }) => {
    if ($isActive) {
      switch ($conflictType) {
        case 'red': return '#FF7262'
        case 'orange': return '#FF9500'
        default: return theme.colorsNew.blue
      }
    }
    return theme.colorsNew.midGrey200
  }};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};

  :after {
    content: ${({ $isActive }) => ($isActive ? "''" : '')};
    display: block;
    width: 100%;
    height: 2px;

    position: absolute;
    bottom: -1px;
    left: 0;

    background: ${({ $isActive, $conflictType }) => {
      if ($isActive) {
        switch ($conflictType) {
          case 'red': return '#FF7262'
          case 'orange': return '#FF9500'
          default: return theme.colorsNew.blue
        }
      }
      return theme.colorsNew.midGrey200
    }};
  }

  :hover,
  :focus {
    color: ${({ $isActive, $conflictType }) => {
      if ($isActive) {
        switch ($conflictType) {
          case 'red': return '#FF7262'
          case 'orange': return '#FF9500'
          default: return theme.colorsNew.blue
        }
      }
      return theme.colorsNew.midGrey200
    }};

    :after {
      content: '';
    }
  }
`

const ConflictIndicatorStyled = styled.div<{ $type: 'red' | 'orange' }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1rem;
  height: 1rem;

  color: ${({ $type }) => $type === 'red' ? '#FF7262' : '#FF9500'};
`

const PlusIconStyled = styled(PlusIcon)`
  width: 1rem;
  height: 1rem;
  fill: currentColor;
`

const RoleBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
`

const RoleBlockLabelStyled = styled.p`
  color: ${theme.colorsNew.midGrey200};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
`

const RoleBlockSalaryStyled = styled.div`
  padding: 0.2rem 0.6rem;
  flex-shrink: 0;

  border-radius: 0.8rem;
  background-color: rgba(69, 84, 104, 0.05);

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
`

const CustomSelectStyled = styled(CustomSelect)<{ $noValue?: boolean }>`
  .Select__control {
    height: 1.8rem;
    min-height: 1.8rem;
    border-color: ${({ $noValue }) => ($noValue ? theme.colorsNew.blue : null)};
    :hover {
      border-color: ${({ $noValue }) =>
        $noValue ? theme.colorsNew.blue : null};
    }
  }
`

const ShiftBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`

const RowCellStyled = styled.div<{ $isShortShift?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.1rem 0;
  position: relative;

  color: ${({ $isShortShift }) =>
    $isShortShift ? '#E18700' : theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  text-align: center;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  :first-of-type {
    justify-content: flex-start;
  }

  :last-of-type {
    justify-content: flex-end;
    padding-left: 0.9rem;
  }
`
const RowStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  grid-template-columns: 1fr 0.7fr 0.7fr 1fr;
  :first-of-type {
    ${RowCellStyled} {
      color: ${theme.colorsNew.midGrey200};
    }
  }
`

const CustomTimePickerStyled = styled(CustomTimePicker)<{ $noValue?: boolean }>`
  height: unset;
  padding: calc(0.1rem - 0.75px) 0.4rem;

  border: 1px solid
    ${({ $noValue }) =>
      $noValue ? theme.colorsNew.blue : 'rgba(164, 170, 185, 0.4)'};
  background: unset;
  border-radius: 0.8rem;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  ::placeholder {
    color: ${theme.colorsNew.blue};
  }
`

const DeleteButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1rem;
  height: 1rem;
  padding: 0;
  margin-left: 0.2rem;

  border: none;
  background-color: transparent;
  color: #afbaca;
  :hover {
    color: ${theme.colors.red};
  }
`

const DeleteIconStyled = styled(DeleteIcon)`
  width: 0.9rem;
  height: 0.9rem;
  fill: currentColor;
`

const BreakBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`

const DividerStyled = styled.div`
  width: 100%;
  height: 1px;
  flex-shrink: 0;
  background-color: #d7dfe9;
`

const CustomCheckboxStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1rem;
  height: 1rem;
  padding: 0;
  margin-left: 0.3rem;

  border: 1px solid
    ${({ $isActive }) => ($isActive ? theme.colorsNew.blue : '#d7dfe9')};
  border-radius: 50%;
  background-color: ${({ $isActive }) =>
    $isActive ? '#fff' : 'rgba(28, 34, 43, 0.04)'};

  :after {
    content: ${({ $isActive }) => ($isActive ? "''" : '')};
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background-color: ${theme.colorsNew.blue};
  }

  :hover {
    border-color: ${theme.colorsNew.blue};
    background-color: #fff;
    box-shadow: 0 0 4px 2px rgba(50, 173, 230, 0.2);
  }
`

const AddBreakButtonStyled = styled.button`
  display: flex;
  align-items: center;
  align-self: flex-end;
  justify-content: center;

  gap: 0.4rem;
  padding: 0.3rem 0;

  border: none;
  background-color: transparent;

  color: ${theme.colorsNew.midGrey200};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  :hover,
  :focus {
    color: ${theme.colorsNew.blue};
  }
`

const TotalBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
`

const TotalBlockLabelStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
`

const TotalBlockValueStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  flex: 1;
  padding: 0.2rem 0.4rem;

  border-radius: 0.8rem;
  background-color: #e8f6eb;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  font-family: ${theme.fonts.bold};
  line-height: normal;
  :first-of-type {
    background-color: #eaeeff;
  }
`

const WarningButtonStyled = styled.button<{ $isRed?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;

  position: absolute;
  left: 0;

  border: 0;
  background-color: transparent;
  color: ${({ $isRed }) => ($isRed ? '#FF7262' : '#FF9500')};
`

const WarningIconStyled = styled(WarningIcon)`
  width: 0.8rem;
  height: 0.8rem;
  fill: currentColor;
`

const TooltipStyled = styled(Popover)<{
  $isRed?: boolean
  $isReclaimTooltip?: boolean
}>`
  min-width: 6rem;
  width: ${({ $isReclaimTooltip }) => ($isReclaimTooltip ? 'unset' : '8.5rem')};
  max-width: ${({ $isReclaimTooltip }) =>
    $isReclaimTooltip ? '12rem' : '8.5rem'};

  padding: ${({ $isReclaimTooltip }) =>
    $isReclaimTooltip ? '0.5rem 1rem' : '0.4rem 1.2rem 0.4rem 0.6rem'};
  margin-top: ${({ $isReclaimTooltip }) =>
    $isReclaimTooltip ? '0.5rem' : '0'};
  margin-bottom: 0.5rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: ${({ $isRed, $isReclaimTooltip }) =>
    $isReclaimTooltip ? '#000' : $isRed ? '#FF7262' : '#fa9702'};

  color: #fff;
  font-size: 0.8rem;
  font-family: ${theme.fonts.regular};
  line-height: normal;

  .arrow {
    left: ${({ $isReclaimTooltip }) =>
      $isReclaimTooltip ? '1rem !important' : null};
    transform: ${({ $isReclaimTooltip }) =>
      $isReclaimTooltip ? 'unset !important' : null};
    :before,
    :after {
      border-top-color: ${({ $isRed, $isReclaimTooltip }) =>
        $isReclaimTooltip ? '#000' : $isRed ? '#FF7262' : '#fa9702'};
    }
  }
`

const CloseButtonTooltipStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;
  position: absolute;
  right: 0.3rem;
  top: 0.3rem;

  border: none;
  background-color: transparent;
`

const CloseIconStyled = styled(CloseIcon)`
  width: 0.55rem;
  height: 0.55rem;

  stroke: #fff;
`

const ScrollBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.8rem;

  max-height: 13.6rem;
  overflow-y: auto;
`

const ButtonBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
`

const ButtonWrapStyled = styled.div`
  display: flex;
  flex: 1;
  & + & {
    justify-content: flex-end;
  }
`

const SaveButtonStyled = styled(OutlineButton)<{
  $isActive?: boolean
  $conflictColor?: 'orange' | 'red' | 'grey' | 'green'
}>`
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;
  min-width: ${({ $isActive }) => ($isActive ? '3rem' : '6rem')};

  position: relative;

  box-shadow: ${({ $isActive }) => ($isActive ? 'none' : null)};
  border-color: ${({ $isActive, $conflictColor }) => {
    if ($isActive) return '#AFBACA'
    switch ($conflictColor) {
      case 'orange': return '#FF9500'
      case 'red': return '#FF7262'
      case 'green': return '#4BCCAD'
      default: return '#AFBACA'
    }
  }};
  border-width: 1px;
  border-radius: 1.2rem;
  background-color: ${({ $conflictColor }) => {
    switch ($conflictColor) {
      case 'orange': return '#FF9500'
      case 'red': return '#FF7262'
      case 'green': return '#4BCCAD'
      default: return '#fff'
    }
  }};
  color: ${({ $isActive, $conflictColor }) => {
    if ($isActive) return '#848DA3'
    return ($conflictColor === 'orange' || $conflictColor === 'red' || $conflictColor === 'green') ? '#fff' : '#000'
  }};
  z-index: 2;
  transition:
    min-width 0.3s ease-in-out,
    border-color 1s ease-in-out,
    background-color 0.3s ease-in-out,
    color 0.3s ease-in-out;
  span:first-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 0 : 1)};
    transition: opacity 0.5s ease-in-out;
  }
  span:last-of-type {
    opacity: ${({ $isActive }) => ($isActive ? 1 : 0)};
    transition: opacity 0.5s ease-in-out;
  }

  :hover,
  :focus {
    box-shadow: ${({ $isActive }) => ($isActive ? 'none' : null)};
    border-color: ${({ $isActive, $conflictColor }) => {
      if ($isActive) return '#AFBACA'
      switch ($conflictColor) {
        case 'orange': return '#FF7A00'
        case 'red': return '#FF5A4A'
        case 'green': return '#3BA892'
        default: return '#4BCCAD'
      }
    }};
    background-color: ${({ $isActive, $conflictColor }) => {
      if ($isActive) return '#fff'
      switch ($conflictColor) {
        case 'orange': return '#FF7A00'
        case 'red': return '#FF5A4A'
        case 'green': return '#3BA892'
        default: return '#4BCCAD'
      }
    }};
    color: ${({ $isActive, $conflictColor }) => {
      if ($isActive) return '#848DA3'
      return ($conflictColor === 'orange' || $conflictColor === 'red' || $conflictColor === 'green') ? '#fff' : '#fff'
    }};
  }
`

const OrangeButtonStyled = styled(OutlineButton)<{
  $isActive?: boolean
}>`
  flex: 1;
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;

  position: relative;

  border: 0;
  border-radius: 1.2rem;
  background: linear-gradient(#ffa100, #ff4d00); // initial gradient

  color: #fff;
  overflow: hidden;
  pointer-events: ${({ $isActive }) => ($isActive ? 'none' : null)};
  z-index: 2;

  p:first-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 0 : 1)};
    transition: opacity 0.3s ease-in-out;
    z-index: 4;
  }

  p:last-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 1 : 0)};
    transition: opacity 1s ease-in-out;
    z-index: 4;
  }
  ::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      90deg,
      #01d9d5 0%,
      #00befa 18.27%,
      #9967ff 42.79%,
      #fd5c61 61.54%,
      #ff902d 84.13%,
      #ffa000 97.12%
    );

    opacity: 0;
    transition: opacity 1s ease-in;
    z-index: 3;
  }
  ::after {
    opacity: ${({ $isActive }) => ($isActive ? 1 : null)};
  }
`

const GreyButtonStyled = styled(OutlineButton)`
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;

  border-width: 1px;
  border-radius: 1.2rem;
  border-color: #848da3;
  background-color: #fff;

  color: #848da3;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const DisabledButtonStyled = styled.button`
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;
  min-width: 6rem;

  border-width: 1px;
  border-radius: 1.2rem;
  border-color: #D1D5DB;
  background-color: #F3F4F6;

  color: #9CA3AF;
  font-family: ${theme.fonts.normal};
  font-size: 0.875rem;
  line-height: normal;
  cursor: not-allowed;

  :hover,
  :focus {
    border-color: #D1D5DB;
    background-color: #F3F4F6;
    color: #9CA3AF;
  }
`
