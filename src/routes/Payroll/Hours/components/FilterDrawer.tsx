import React from 'react'
import useOnclickOutside from 'react-cool-onclickoutside'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import {
  DepartmentRoles,
  RoleFilterState,
  RoleFilterItem,
  DepartmentType,
  saveRoleFilterState
} from 'utils/payroll/roleFilterUtils'

interface FilterDrawerProps {
  show: boolean
  drawerRef: ReturnType<typeof useOnclickOutside>
  departmentRoles: DepartmentRoles
  filterState: RoleFilterState
  onFilterChange: (newState: RoleFilterState) => void
  isSingleRole: boolean
  singleRoleInfo?: {
    department: DepartmentType
    role: RoleFilterItem
  } | null
  onSwitchToRoles?: () => void
  currentDisplayBy: string
  companyKey?: string
}

interface DrawerBlockProps {
  title: string
  department: DepartmentType
  roles: RoleFilterItem[]
  filterState: RoleFilterState
  onFilterChange: (newState: RoleFilterState) => void
  onSwitchToRoles?: () => void
  currentDisplayBy: string
  companyKey?: string
}

const DrawerBlock: React.FC<DrawerBlockProps> = ({
  title,
  department,
  roles,
  filterState,
  onFilterChange,
  onSwitchToRoles,
  currentDisplayBy,
  companyKey
}) => {
  const selectedRoleIds = filterState.selectedRoles[department] || []
  // Department is considered "selected" if ALL roles in the department are selected
  const isDepartmentSelected = selectedRoleIds.length === roles.length

  const handleSwitchClick = () => {
    const newState = { ...filterState }

    if (isDepartmentSelected) {
      // Deselect all roles in this department
      newState.selectedRoles[department] = []
      roles.forEach(role => {
        newState.selectedSubcategories[role.id] = []
      })

      // Remove department from selectedDepartments if no roles are selected
      newState.selectedDepartments = newState.selectedDepartments.filter(d => d !== department)
    } else {
      // Select all roles in this department
      newState.selectedRoles[department] = roles.map(role => role.id)
      roles.forEach(role => {
        newState.selectedSubcategories[role.id] = role.subcategories.map(sub => sub.id)
      })

      // Add department to selectedDepartments if not already there
      if (!newState.selectedDepartments.includes(department)) {
        newState.selectedDepartments = [...newState.selectedDepartments, department]
      }
    }

    onFilterChange(newState)
    saveRoleFilterState(companyKey || 'default', newState)

    // Auto-switch to "Roles" view when filter changes, but only if currently in "Employees" view
    if (onSwitchToRoles && currentDisplayBy === 'employees') {
      onSwitchToRoles()
    }
  }

  const handleRoleClick = (roleId: string) => {
    const newState = { ...filterState }
    const role = roles.find(r => r.id === roleId)
    if (!role) return

    const currentSelectedRoles = newState.selectedRoles[department] || []
    const isRoleSelected = currentSelectedRoles.includes(roleId)

    if (isRoleSelected) {
      // Deselect role
      const remainingRoles = currentSelectedRoles.filter(id => id !== roleId)
      newState.selectedSubcategories[roleId] = []
      newState.selectedRoles[department] = remainingRoles

      // Update department selection based on remaining roles
      if (remainingRoles.length === 0) {
        // If no roles left in department, remove department from selectedDepartments
        newState.selectedDepartments = newState.selectedDepartments.filter(d => d !== department)
      }
    } else {
      // Select role
      newState.selectedRoles[department] = [...currentSelectedRoles, roleId]
      newState.selectedSubcategories[roleId] = role.subcategories.map(sub => sub.id)

      // Add department to selectedDepartments if not already there
      if (!newState.selectedDepartments.includes(department)) {
        newState.selectedDepartments = [...newState.selectedDepartments, department]
      }
    }

    onFilterChange(newState)
    saveRoleFilterState(companyKey || 'default', newState)

    // Auto-switch to "Roles" view when filter changes, but only if currently in "Employees" view
    if (onSwitchToRoles && currentDisplayBy === 'employees') {
      onSwitchToRoles()
    }
  }

  return (
    <DrawerBlockStyled>
      <DrawerBlockHeaderStyled>
        <DrawerBlockHeaderTitleStyled>{title}</DrawerBlockHeaderTitleStyled>
        <SwitchStyled
          $isActive={isDepartmentSelected}
          onClick={handleSwitchClick}
        >
          <SwitchButtonStyled />
        </SwitchStyled>
      </DrawerBlockHeaderStyled>
      <DrawerBlockListStyled>
        {roles.map((role) => (
          <ListItemStyled
            $isSelected={selectedRoleIds.includes(role.id)}
            key={role.id}
            onClick={() => handleRoleClick(role.id)}
          >
            {role.name}
          </ListItemStyled>
        ))}
      </DrawerBlockListStyled>
    </DrawerBlockStyled>
  )
}

const FilterDrawer = ({
  show,
  drawerRef,
  departmentRoles,
  filterState,
  onFilterChange,
  isSingleRole,
  singleRoleInfo,
  onSwitchToRoles,
  currentDisplayBy,
  companyKey
}: FilterDrawerProps) => {
  // For single role companies, show only that department and role
  if (isSingleRole && singleRoleInfo) {
    return (
      <DrawerStyled
        $isVisible={show}
        ref={drawerRef}
      >
        <SingleRoleMessageStyled>
          {I18n.t('payroll.filter')} - {getDepartmentTitle(singleRoleInfo.department)}: {singleRoleInfo.role.name}
        </SingleRoleMessageStyled>
        <DrawerBlock
          title={getDepartmentTitle(singleRoleInfo.department)}
          department={singleRoleInfo.department}
          roles={[singleRoleInfo.role]}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
          companyKey={companyKey}
        />
      </DrawerStyled>
    )
  }

  // Standard filter for companies with multiple roles
  return (
    <DrawerStyled
      $isVisible={show}
      ref={drawerRef}
    >
      {departmentRoles.service.length > 0 && (
        <DrawerBlock
          title={getDepartmentTitle('service')}
          department='service'
          roles={departmentRoles.service}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
          companyKey={companyKey}
        />
      )}

      {departmentRoles.kitchen.length > 0 && (
        <DrawerBlock
          title={getDepartmentTitle('kitchen')}
          department='kitchen'
          roles={departmentRoles.kitchen}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
          companyKey={companyKey}
        />
      )}

      {departmentRoles.bar.length > 0 && (
        <DrawerBlock
          title={getDepartmentTitle('bar')}
          department='bar'
          roles={departmentRoles.bar}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
          companyKey={companyKey}
        />
      )}

      {departmentRoles.management.length > 0 && (
        <DrawerBlock
          title={getDepartmentTitle('management')}
          department='management'
          roles={departmentRoles.management}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
          companyKey={companyKey}
        />
      )}
    </DrawerStyled>
  )
}

const getDepartmentTitle = (department: DepartmentType): string => {
  switch (department) {
    case 'service':
      return I18n.t('schedule.frontOfHouse')
    case 'kitchen':
      return I18n.t('schedule.backOfHouse')
    case 'bar':
      return I18n.t('schedule.bar')
    case 'management':
      return I18n.t('schedule.management')
    default:
      return department
  }
}

export default FilterDrawer

const DrawerStyled = styled.div<{ $isVisible: boolean }>`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 2rem;
  width: 15rem;
  height: 100vh;
  padding: 2rem;
  min-width: 300px;

  position: fixed;
  top: 0;
  right: 0;
  pointer-events: ${({ $isVisible }) => ($isVisible ? 'auto' : 'none')};
  background-color: #fff;

  opacity: ${({ $isVisible }) => ($isVisible ? 1 : 0)};
  visibility: ${({ $isVisible }) => ($isVisible ? 'visible' : 'hidden')};
  transform: translateX(${({ $isVisible }) => ($isVisible ? '0' : '100%')});
  transition: all 0.3s ease-in-out;
  overflow-y: auto;

  box-shadow: -30px 44px 44px 0px rgba(0, 0, 0, 0.2);
  z-index: 1000;
`

const DrawerBlockStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 1rem;
  width: 100%;
`

const DrawerBlockHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
`

const DrawerBlockHeaderTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

const DrawerBlockListStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 0.5rem;
  width: 100%;
`

const ListItemStyled = styled.button<{ $isSelected: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: 0.5rem 0.8rem;

  border: 1.5px solid
    ${({ $isSelected }) => ($isSelected ? '#00b1ff' : '#e4e7ec')};
  border-radius: 0.8rem;
  background-color: ${({ $isSelected }) => ($isSelected ? '#fff' : '#f2f2f4')};

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.875rem;
  font-family: ${theme.fonts.normal};
  :hover {
    border-color: #00b1ff;
  }
`

const SwitchStyled = styled.button<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: ${({ $isActive }) =>
    $isActive ? 'flex-end' : 'flex-start'};

  width: 2.4rem;
  height: 1.4rem;
  padding: 0.2rem;

  border: 0;
  background-color: ${({ $isActive }) => ($isActive ? '#00A2E9' : '#e9eff6')};
  border-radius: 1.2rem;
  transition: all 0.2s ease-in-out;
  :hover,
  :focus {
    box-shadow: 0px 0px 4px 0px rgba(18, 18, 23, 0.5);
  }
`

const SwitchButtonStyled = styled.div`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: transparent;
  border: 0.3rem solid #fff;
  box-shadow:
    0px 1px 2px 0px rgba(18, 18, 23, 0.06),
    0px 1px 3px 0px rgba(18, 18, 23, 0.1);
`

const SingleRoleMessageStyled = styled.div`
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.8rem;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.875rem;
  font-family: ${theme.fonts.normal};
  text-align: center;
  line-height: 1.4;
`
