import React, { useState } from 'react'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { ReactComponent as CheckIcon } from 'img/icons/checkCircledTransIcon.svg'
import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'
import { ReactComponent as StarIcon } from 'img/icons/sparkleIcon.svg'
import { ReactComponent as TrashIcon } from 'img/icons/trashFilledIcon.svg'

const PayrollToaster = ({
  onUndo,
  status
}: {
  onUndo?: () => void
  status?: string
}) => {
  const [isProcessing, setIsProcessing] = useState(false)
  let statusText = ''
  let statusIcon = <></>
  let bgColor = ''
  switch (status) {
    case 'claim':
      statusText = I18n.t('payroll.you_have_claimed_shift_early_shift')
      statusIcon = <StarIcon />
      bgColor =
        'linear-gradient(93.61deg, #01D9D5 -15.11%, #00BEFA 20.45%, #CF90FF 63.46%, #FD5C61 86.88%, #FF902D 97.85%, #FF912A 105.67%, #FFA000 115.28%)'
      break
    case 'delete':
      statusText = I18n.t('payroll.you_have_deleted_shift')
      statusIcon = <TrashIcon />
      bgColor = '#FF7262'
      break
    case 'approve':
      statusText = I18n.t('payroll.you_have_approved_shift')
      statusIcon = <CheckIcon />
      bgColor = '#4BCCAD'
      break
    case 'modify':
      statusText = I18n.t('payroll.shift_update_has_been_saved')
      statusIcon = <CheckIcon />
      bgColor = '#32ADE6'
      break
  }

  return (
    <ToasterStyled $bgColor={bgColor}>
      <HeaderStyled>
        <IconWrapStyled>{statusIcon}</IconWrapStyled>
        <CloseIconStyled />
      </HeaderStyled>
      <BlockStyled>
        <TextStyled>{statusText}</TextStyled>
        {onUndo && (
          <ButtonStyled
            onClick={() => {
              setIsProcessing(true)
              onUndo()
            }}
            disabled={isProcessing}
          >
            {I18n.t('common.undo')}
          </ButtonStyled>
        )}
      </BlockStyled>
    </ToasterStyled>
  )
}

export default PayrollToaster

const ToasterStyled = styled.div<{ $bgColor: string }>`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 0.8rem;
  padding: 0.8rem;
  border-radius: 0.6rem;

  background: ${({ $bgColor }) => $bgColor};
`

const CloseIconStyled = styled(CloseIcon)`
  width: 0.8rem;
  height: 0.8rem;
  stroke: #fff;
`

const HeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`

const IconWrapStyled = styled.div`
  svg {
    width: 1.2rem;
    height: 1.2rem;
  }
`

const BlockStyled = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: space-between;

  gap: 1rem;
  width: 100%;
`

const TextStyled = styled.p`
  color: ${theme.colors.white};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const ButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.2rem 1rem;
  min-height: 2rem;
  flex-shrink: 0;

  border: 1px solid #fff;
  border-radius: 1.2rem;
  background: transparent;

  color: ${theme.colors.white};
  font-size: 0.8rem;
  font-family: ${theme.fonts.bold};
`
