import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'

import { RootState } from 'store/reducers'
import styled from 'styled-components'
import { theme } from 'styles/theme'

interface RollingNumberProps {
  value: number
}
interface DigitState {
  value: string
  isSeparator: boolean
}
const ROLL_DURATION = '750ms'
const ANIMATION_DELAY = 50
const ANIMATION_BUFFER = 200
const DIGITS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] as const
const RollingNumber = ({ value }: RollingNumberProps) => {
  const [digitStates, setDigitStates] = useState<DigitState[]>([])
  const [isAnimating, setIsAnimating] = useState<boolean>(false)
  const [animationValues, setAnimationValues] = useState<string[]>([])
  const isLocaleFr =
    useSelector((state: RootState) => state.i18n.locale) === 'fr'
  // Memoized number formatting function
  const formatNumber = useCallback(
    (value: number): string => {
      const parts = value.toFixed(2).split('.')
      const integerPart = parts[0]
      const decimalPart = parts[1]
      const formattedInteger = integerPart.replace(
        /\B(?=(\d{3})+(?!\d))/g,
        isLocaleFr ? ' ' : ','
      )
      return `${formattedInteger}${isLocaleFr ? ',' : '.'}${decimalPart}`
    },
    [isLocaleFr]
  )
  // Memoized transform styles
  const transformStyles = useMemo(
    () => ({
      '\u200B': 'translateY(10%)',
      '0': 'translateY(0)',
      '1': 'translateY(-10%)',
      '2': 'translateY(-20%)',
      '3': 'translateY(-30%)',
      '4': 'translateY(-40%)',
      '5': 'translateY(-50%)',
      '6': 'translateY(-60%)',
      '7': 'translateY(-70%)',
      '8': 'translateY(-80%)',
      '9': 'translateY(-90%)'
    }),
    []
  )
  const getTransformStyle = useCallback(
    (digitValue: string): string => {
      return (
        transformStyles[digitValue as keyof typeof transformStyles] ||
        'translateY(0)'
      )
    },
    [transformStyles]
  )
  // Parse formatted number into digit states
  const parseFormattedNumber = useCallback(
    (formattedValue: string): DigitState[] => {
      return formattedValue.split('').map(digit => ({
        value: digit,
        isSeparator: !/\d/.test(digit)
      }))
    },
    []
  )
  // Generate random starting digits for animation
  const generateRandomDigits = useCallback(
    (finalDigits: string[]): string[] => {
      return finalDigits.map(char =>
        /\d/.test(char) ? Math.floor(Math.random() * 10).toString() : char
      )
    },
    []
  )
  // Start animation sequence
  const startAnimation = useCallback(
    (finalDigits: string[]) => {
      setIsAnimating(true)
      const startDigits = generateRandomDigits(finalDigits)
      setAnimationValues(startDigits)
      const timeout = setTimeout(() => {
        setAnimationValues(finalDigits)
      }, ANIMATION_DELAY)
      const resetTimeout = setTimeout(
        () => {
          setIsAnimating(false)
        },
        parseInt(ROLL_DURATION) + ANIMATION_BUFFER
      )
      return () => {
        clearTimeout(timeout)
        clearTimeout(resetTimeout)
      }
    },
    [generateRandomDigits]
  )
  // Handle value changes and start animation
  useEffect(() => {
    const formattedValue = formatNumber(value)
    const newDigitStates = parseFormattedNumber(formattedValue)
    setDigitStates(newDigitStates)
    // Start animation with new values
    const cleanup = startAnimation(formattedValue.split(''))
    return cleanup
  }, [value, formatNumber, parseFormattedNumber, startAnimation])
  return (
    <ContainerStyled>
      {isLocaleFr ? '' : '$ '}
      {digitStates.map((digitState, index) => {
        const displayValue =
          isAnimating && animationValues[index]
            ? animationValues[index]
            : digitState.value
        return (
          <DigitStyled
            key={`digit-${index}`}
            $isSeparator={digitState.isSeparator}
          >
            {digitState.isSeparator ? (
              <SeparatorValueStyled>{displayValue}</SeparatorValueStyled>
            ) : (
              <>
                <ScaleStyled
                  $transform={getTransformStyle(displayValue)}
                  $isAnimating={isAnimating}
                  $rollDuration={ROLL_DURATION}
                  aria-hidden='true'
                >
                  {DIGITS.map(digit => (
                    <DigitSpanStyled
                      key={digit}
                      $isActive={digitState.value === digit.toString()}
                    >
                      {digit}
                    </DigitSpanStyled>
                  ))}
                </ScaleStyled>
                <ValueStyled>&nbsp;</ValueStyled>
              </>
            )}
          </DigitStyled>
        )
      })}
      {isLocaleFr ? <>&nbsp;$</> : ''}
    </ContainerStyled>
  )
}
export default RollingNumber
const ContainerStyled = styled.div`
  font-size: 18px;
  font-family: ${theme.fonts.bold};
  @media (max-width: 1920px) {
    font-size: 16px;
  }
  @media (max-width: 1560px) {
    font-size: 14px;
  }
  @media (max-width: 1440px) {
    font-size: 12px;
  }
  @media (max-width: 1366px) {
    font-size: 10px;
  }
`
const DigitStyled = styled.span<{ $isSeparator: boolean }>`
  display: inline-flex;
  width: ${({ $isSeparator }) => ($isSeparator ? 'auto' : '1ch')};
  position: relative;
  overflow: hidden;
`
const ValueStyled = styled.span`
  color: transparent;
  position: relative;
`
const SeparatorValueStyled = styled.span`
  color: inherit;
  position: relative;
`
interface ScaleProps {
  $transform: string
  $isAnimating: boolean
  $rollDuration: string
}
const ScaleStyled = styled.span<ScaleProps>`
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  position: absolute;
  left: 0;
  transform: ${props => props.$transform};
  transition: ${props =>
    props.$isAnimating ? `transform ${props.$rollDuration} ease-out` : 'none'};
  user-select: none;
`
const DigitSpanStyled = styled.span<{ $isActive: boolean }>`
  color: #455468;
  opacity: ${({ $isActive }) => ($isActive ? 1 : 0.5)};
`
