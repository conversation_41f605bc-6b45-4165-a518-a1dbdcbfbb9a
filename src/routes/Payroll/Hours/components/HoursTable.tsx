import React, { useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import useOnclickOutside from 'react-cool-onclickoutside'
import { I18n } from 'react-redux-i18n'

import dayjs, { Dayjs } from 'dayjs'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { AvatarInitials } from 'components/ui/AvatarInitials'
import { Input } from 'components/ui/Input'
import NumberFormatted from 'components/ui/NumberFormatted'

import FilterDrawer from './FilterDrawer'
import ShiftPopover from './ShiftPopover'

import {
  DepartmentRoles,
  DepartmentType,
  RoleFilterItem,
  RoleFilterState,
  filterEmployeesByRoles
} from 'utils/payroll/roleFilterUtils'
import {
  analyzeShiftStatus,
  getShiftStatusColor,
  getShiftStatusDescription
} from 'utils/payroll/shiftStatusAnalysis'
import { containsString } from 'utils/removeDiacriticsString'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company, IPosition } from 'types/company'
import { IEmployee, IEmployeePosition } from 'types/employee'

import arrowDownIcon from 'img/icons/arrowDownEqualIcon.svg'
import { ReactComponent as FilterIcon } from 'img/icons/filterIcon.svg'
import { ReactComponent as ClockIcon } from 'img/icons/hoursIcon.svg'
import { ReactComponent as PlusIcon } from 'img/icons/plusIcon.svg'
import { ReactComponent as SearchIcon } from 'img/icons/searchIcon.svg'

interface HoursTableProps {
  employeesArray: Array<{
    id: number
    name: string
    surname: string
    avatar: string
    userId: string
    uid: string
    positions?: IEmployeePosition[]
    payrollId?: string
    customId?: string
  }>
  employeesByRole: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  }
  searchEmployee: string
  onSearchEmployee: (value: string) => void
  displayBy: string
  setDisplayBy: (value: string) => void
  displayByArray: Array<{ id: string; label: string; icon: React.ReactNode }>
  attendanceData: AttendanceShifts
  currentCompany: Company
  isDataLoaded: boolean
  selectedPositionId: string
  setSelectedPositionId: (value: string) => void
  startOfPeriod: Dayjs
  currentPeriodOffset: number
  payrollLength: number
  onSave: (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string
  ) => void
  onDeleteShift: (shiftKey: string, employeeId: string, date: string) => void
  departmentRoles: DepartmentRoles
  roleFilterState: RoleFilterState
  onRoleFilterChange: (newState: RoleFilterState) => void
  isSingleRole: boolean
  singleRoleInfo?: {
    department: DepartmentType
    role: RoleFilterItem
  } | null
  hasPayrollIntegration: boolean
}

// Helper function to get consistent employee ID
const getEmployeeId = (
  employee: IEmployee | { uid: string; userId?: string; [key: string]: unknown }
): string => {
  // Try uid first, then userId, then fallback to empty string
  const id = employee.uid || ('userId' in employee ? employee.userId : '') || ''
  // Ensure we return a string and handle null/undefined cases
  return id ? String(id) : ''
}

// Helper function to calculate employee weekly stats
const getEmployeeWeeklyStats = (
  employeeUid: string,
  attendanceData: AttendanceShifts,
  startOfPeriod: Dayjs,
  payrollLength: number
) => {
  // startOfPeriod is already the correct period start (offset already applied in parent)
  const currentPeriodStart = startOfPeriod.clone()
  const numberOfWeeks = payrollLength === 14 ? 2 : 1

  const weeklyStats = []
  for (let weekIndex = 0; weekIndex < numberOfWeeks; weekIndex++) {
    // Calculate week start based on the period start + (weekIndex * 7 days)
    // This ensures we follow the payroll period exactly, not calendar weeks
    const weekStart = currentPeriodStart.clone().add(weekIndex * 7, 'days')
    const weekEnd = weekStart.clone().add(6, 'days')

    let weekHours = 0
    let weekSalary = 0

    // Iterate through each day in the week
    let currentDay = weekStart.clone()
    while (currentDay.isSameOrBefore(weekEnd)) {
      const dateKey = currentDay.format('YYYY-MM-DD')
      const dayShifts = attendanceData[dateKey]

      if (dayShifts && dayShifts[employeeUid]) {
        for (const shift of Object.values(dayShifts[employeeUid])) {
          const shiftData = shift as AttendanceShift & {
            shiftLengthHours?: number
            salary?: number
          }

          // Calculate hours
          if (shiftData.shiftLengthHours) {
            weekHours += shiftData.shiftLengthHours
          } else if (shiftData.start && shiftData.end) {
            const shiftHours = (shiftData.end - shiftData.start) / 60
            weekHours += shiftHours > 0 ? shiftHours : 0
          }

          // Add salary
          if (shiftData.salary) {
            weekSalary += shiftData.salary
          }
        }
      }

      currentDay = currentDay.add(1, 'day')
    }

    weeklyStats.push({
      weekStart: weekStart.format('YYYY-MM-DD'),
      weekEnd: weekEnd.format('YYYY-MM-DD'),
      hours: weekHours,
      salary: weekSalary
    })
  }

  return { weeklyStats }
}

// Helper function to get shifts for a specific employee and date
const getEmployeeShiftsForDate = (
  employeeUid: string,
  date: string,
  attendanceData: AttendanceShifts
) => {
  const dayShifts = attendanceData[date]
  if (!dayShifts || !dayShifts[employeeUid]) {
    return []
  }

  return Object.entries(dayShifts[employeeUid]).map(([shiftKey, shift]) => ({
    ...shift,
    shiftKey
  }))
}

// Helper function to format time from minutes to HH:MM
const formatTime = (minutes: number | undefined | null): string => {
  if (minutes === undefined || minutes === null) return '--:--'
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

// Helper function to calculate break duration
const calculateBreakDuration = (
  breaks: { [key: string]: { start?: number; end?: number } } | undefined
): number => {
  if (!breaks || typeof breaks !== 'object') return 0

  let totalBreakTime = 0
  Object.values(breaks).forEach(breakItem => {
    if (breakItem.start && breakItem.end) {
      totalBreakTime += breakItem.end - breakItem.start
    }
  })

  return totalBreakTime
}

const HoursTable = ({
  employeesArray,
  employeesByRole,
  searchEmployee,
  onSearchEmployee,
  displayBy,
  setDisplayBy,
  displayByArray,
  attendanceData,
  currentCompany,
  isDataLoaded,
  selectedPositionId,
  setSelectedPositionId,
  startOfPeriod,
  currentPeriodOffset,
  payrollLength,
  onSave,
  onDeleteShift,
  departmentRoles,
  roleFilterState,
  onRoleFilterChange,
  isSingleRole,
  singleRoleInfo,
  hasPayrollIntegration
}: HoursTableProps) => {
  const [expandedRows, setExpandedRows] = useState<{ [id: string]: boolean }>(
    {}
  )
  const [showFilterDrawer, setShowFilterDrawer] = useState(false)

  // Generate days array for the current period
  const daysArray = React.useMemo(() => {
    // startOfPeriod is already the correct period start (offset already applied in parent)
    const currentPeriodStart = startOfPeriod.clone()
    const days = []

    for (let i = 0; i < payrollLength; i++) {
      const day = currentPeriodStart.clone().add(i, 'days')
      days.push({
        date: day.format('YYYY-MM-DD'),
        dayName: day.format('ddd'),
        dayNumber: day.format('D'),
        isToday: day.isSame(dayjs(), 'day')
      })
    }

    return days
  }, [startOfPeriod, payrollLength])

  const filteredEmployees = React.useMemo(() => {
    // First filter by search term
    let filtered = employeesArray.filter(employee => {
      const employeeName = employee.name + ' ' + employee.surname
      return containsString(employeeName, searchEmployee)
    })

    // Apply role filtering only for "By Roles" view
    if (displayBy === 'role') {
      filtered = filterEmployeesByRoles(
        filtered,
        roleFilterState,
        currentCompany.jobs || {}
      )
    }

    // Apply specific position filter if selected
    if (selectedPositionId) {
      filtered = filtered.filter(employee =>
        (employee.positions || []).some(
          (pos: IEmployeePosition) =>
            pos.subcategoryId === selectedPositionId ||
            pos.categoryId === selectedPositionId
        )
      )
    }

    // Sort by first name to maintain alphabetical order
    filtered.sort((a, b) => {
      const aName = a.name || ''
      const bName = b.name || ''
      return aName.localeCompare(bName)
    })

    return filtered
  }, [
    employeesArray,
    searchEmployee,
    displayBy,
    roleFilterState,
    selectedPositionId,
    currentCompany.jobs
  ])

  // Calculate total hours and salary for the current period
  const { totalHours, totalSalary } = React.useMemo(() => {
    let hours = 0
    let salary = 0

    const currentPeriodStart = startOfPeriod
      .clone()
      .add(currentPeriodOffset * payrollLength, 'days')
    const currentPeriodEnd = currentPeriodStart
      .clone()
      .add(payrollLength - 1, 'days')

    // Iterate through each day in the current period
    let currentDay = currentPeriodStart.clone()
    while (currentDay.isSameOrBefore(currentPeriodEnd)) {
      const dateKey = currentDay.format('YYYY-MM-DD')
      const dayShifts = attendanceData[dateKey]

      if (dayShifts) {
        // Calculate for all employees on this day
        for (const employee of filteredEmployees) {
          const employeeShifts = dayShifts[employee.uid]
          if (employeeShifts) {
            for (const shift of Object.values(employeeShifts)) {
              const shiftData = shift as AttendanceShift & {
                shiftLengthHours?: number
                salary?: number
              }
              // Calculate hours from start/end times or use shiftLengthHours if available
              if (shiftData.shiftLengthHours) {
                hours += shiftData.shiftLengthHours
              } else if (shiftData.start && shiftData.end) {
                const shiftHours = (shiftData.end - shiftData.start) / 60
                hours += shiftHours > 0 ? shiftHours : 0
              }

              // Add salary if available
              if (shiftData.salary) {
                salary += shiftData.salary
              }
            }
          }
        }
      }

      currentDay = currentDay.add(1, 'day')
    }

    return { totalHours: hours, totalSalary: salary }
  }, [
    attendanceData,
    filteredEmployees,
    startOfPeriod,
    currentPeriodOffset,
    payrollLength
  ])

  // Filter employeesByRole based on search term, role filter, and position filter
  const filteredEmployeesByRole = React.useMemo(() => {
    const filtered: {
      [roleId: string]: { role: IPosition; employees: IEmployee[] }
    } = {}

    Object.entries(employeesByRole).forEach(([roleId, roleData]) => {
      const job = currentCompany.jobs?.[roleId]
      if (!job || job.archived) return

      // Check if this role is selected in the filter
      const department = job.type || 'FOH'
      const isRoleSelected =
        roleFilterState.selectedDepartments.includes(department) &&
        roleFilterState.selectedRoles[department]?.includes(roleId)

      // Only include roles that are selected in the filter
      if (!isRoleSelected) {
        return // Skip this role entirely if not selected
      }

      // First filter by search term
      let filteredRoleEmployees = roleData.employees.filter(employee => {
        const employeeName = employee.name + ' ' + employee.surname
        return containsString(employeeName, searchEmployee)
      })

      // Note: We don't need to call filterEmployeesByRoles here because:
      // 1. We already checked if the role is selected above (lines 357-359)
      // 2. The employees in this roleData already belong to this specific role
      // 3. Additional role filtering would be redundant and might cause issues

      // Apply specific position filter if selected
      if (selectedPositionId) {
        filteredRoleEmployees = filteredRoleEmployees.filter(employee =>
          (employee.positions || []).some(
            (pos: IEmployeePosition) =>
              pos.subcategoryId === selectedPositionId ||
              pos.categoryId === selectedPositionId
          )
        )
      }

      // Sort employees by first name within each role
      filteredRoleEmployees.sort((a, b) => {
        const aName = a.name || ''
        const bName = b.name || ''
        return aName.localeCompare(bName)
      })

      filtered[roleId] = {
        role: roleData.role,
        employees: filteredRoleEmployees
      }
    })

    return filtered
  }, [
    employeesByRole,
    searchEmployee,
    roleFilterState,
    selectedPositionId,
    currentCompany.jobs
  ])

  const filterButtonRef = useRef<HTMLButtonElement>(null)
  const drawerRef = useOnclickOutside(
    (e: Event) => {
      if (!filterButtonRef.current?.contains(e.target as Node)) {
        setShowFilterDrawer(false)
      }
    },
    { disabled: !showFilterDrawer }
  )

  const [isEmployeeExpanded, setIsEmployeeExpanded] = useState<{
    [id: string | number]: boolean
  }>({})

  // Calculate current day index - only highlight when viewing current period (offset 0)
  const getCurrentDayIndex = React.useMemo(() => {
    // Only highlight current day when viewing the current period
    if (currentPeriodOffset !== 0) {
      return null // No highlighting for past/future periods
    }

    const today = dayjs().format('YYYY-MM-DD')
    const idx = daysArray.findIndex(day => day.date === today)
    return idx >= 0 ? idx : null
  }, [daysArray, currentPeriodOffset])

  const [activeShiftPopover, setActiveShiftPopover] = useState<string | null>(
    null
  )
  const shiftRefs = useRef<Map<string, HTMLElement | null>>(new Map())

  // Parse active shift popover to get employee, date, and shift info
  const activeShiftInfo = React.useMemo(() => {
    if (!activeShiftPopover) {
      return null
    }
    // Parse shiftId by finding the last two dashes (for dayIndex and shiftIndex)
    // This handles UIDs with multiple internal dashes like '-MCMlGbLjD1rD3fEt-OG'
    const lastDashIndex = activeShiftPopover.lastIndexOf('-')
    const secondLastDashIndex = activeShiftPopover.lastIndexOf(
      '-',
      lastDashIndex - 1
    )
    if (lastDashIndex === -1 || secondLastDashIndex === -1) {
      console.log('Invalid shiftId format:', activeShiftPopover)
      return null
    }
    const employeeId = activeShiftPopover.substring(0, secondLastDashIndex)
    // Fix: dayIndex should match the index in daysArray, which is the index in the map, not the day of week
    // The index in the shiftId is the index in daysArray, so use it directly
    const dayIndex = Number(
      activeShiftPopover.substring(secondLastDashIndex + 1, lastDashIndex)
    )
    const shiftIndex = activeShiftPopover.substring(lastDashIndex + 1)
    // Find the employee by uid (use filteredEmployees since that's what's displayed)
    const employee = filteredEmployees.find(
      emp => getEmployeeId(emp) === employeeId
    )
    if (!employee) {
      return null
    }
    // Defensive: clamp dayIndex to valid range
    let safeDayIndex = dayIndex
    if (
      isNaN(safeDayIndex) ||
      safeDayIndex < 0 ||
      safeDayIndex >= daysArray.length
    ) {
      return null
    }
    const day = daysArray[safeDayIndex]
    if (!day) {
      return null
    }
    const dayShifts = getEmployeeShiftsForDate(
      getEmployeeId(employee),
      day.date,
      attendanceData
    )
    return {
      employee,
      date: day.date,
      dayIndex: safeDayIndex,
      shiftIndex,
      shifts: dayShifts,
      isNewShift: shiftIndex === 'add'
    }
  }, [activeShiftPopover, filteredEmployees, daysArray, attendanceData])

  /**
   * Determines the overall status for an employee based on all their shifts
   * Returns the highest priority status found across all shifts
   */
  const getEmployeeOverallStatus = (employee: any): { status: string; color: string } => {
    let highestPriority = 0
    let overallStatus = 'approved'

    // Check all shifts for this employee across all days
    daysArray.forEach(day => {
      const dayShifts = getEmployeeShiftsForDate(
        getEmployeeId(employee),
        day.date,
        attendanceData
      )

      dayShifts.forEach(shift => {
        const shiftAnalysis = analyzeShiftStatus(
          shift,
          getEmployeeId(employee),
          day.date,
          attendanceData,
          currentCompany,
          day.isToday
        )

        // If this shift has higher priority, update overall status
        if (shiftAnalysis.priority > highestPriority) {
          highestPriority = shiftAnalysis.priority
          overallStatus = shiftAnalysis.status
        }
      })
    })

    const statusColor = getShiftStatusColor(overallStatus as any)
    return { status: overallStatus, color: statusColor }
  }

  // Enhanced popperConfig for robust positioning, especially for Sunday
  // TODO Lie - ?
  // That's should be hanlded with default props from Overlay
  const refinedPopperConfig = React.useMemo(() => {
    let extraOffset = 8
    let forcePlacement: 'left' | 'right' | 'bottom' | undefined = undefined
    if (activeShiftPopover) {
      const lastDashIndex = activeShiftPopover.lastIndexOf('-')
      const secondLastDashIndex = activeShiftPopover.lastIndexOf(
        '-',
        lastDashIndex - 1
      )
      if (lastDashIndex !== -1 && secondLastDashIndex !== -1) {
        // Use the same dayIndex logic as activeShiftInfo
        const dayIndex = Number(
          activeShiftPopover.substring(secondLastDashIndex + 1, lastDashIndex)
        )
        // Defensive: clamp dayIndex to valid range
        let safeDayIndex = dayIndex
        if (
          isNaN(safeDayIndex) ||
          safeDayIndex < 0 ||
          safeDayIndex >= daysArray.length
        ) {
          safeDayIndex = 0
        }
        // Use payrollStartingDay to match popover logic
        let payrollStartingDay = currentCompany?.payrollStartingDay || 'Monday'
        const weekDays = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday'
        ]
        let startIdx = weekDays.findIndex(
          d => d.toLowerCase() === payrollStartingDay.toLowerCase()
        )
        if (startIdx === -1) startIdx = 0
        const columnPosition = (safeDayIndex + startIdx) % 7
        // Rightmost columns (Fri/Sat/Sun): force left, add offset
        if (columnPosition === 6) {
          // Sunday
          extraOffset = 120
          forcePlacement = 'left'
        } else if (columnPosition === 5) {
          // Saturday
          extraOffset = 80
          forcePlacement = 'left'
        } else if (columnPosition === 4) {
          // Friday
          extraOffset = 64
          forcePlacement = 'left'
        } else if (columnPosition === 3) {
          // Thursday
          extraOffset = 48
          forcePlacement = 'left'
        } else if (columnPosition === 2) {
          // Wednesday
          extraOffset = 64
          forcePlacement = 'right'
        } else if (columnPosition === 1) {
          // Tuesday
          extraOffset = 48
          forcePlacement = 'right'
        } else if (columnPosition === 0) {
          // Monday
          extraOffset = 64
          forcePlacement = 'right'
        }
      }
    }
    return {
      placement: forcePlacement,
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            boundary: 'viewport',
            padding: 10,
            mainAxis: true,
            altAxis: true,
            tether: false
          }
        },
        {
          name: 'flip',
          options: {
            fallbackPlacements: ['bottom', 'left', 'right', 'top']
          }
        },
        {
          name: 'offset',
          options: {
            offset: [0, extraOffset]
          }
        },
        {
          name: 'customMaxRight',
          enabled: true,
          phase: 'main' as const,
          fn({ state }: { state: Record<string, unknown> }) {
            const stateTyped = state as {
              rects: { popper: { width: number; height: number } }
              placement: string
              styles: { popper: Record<string, string> }
            }
            const { popper } = stateTyped.rects
            const { placement, styles } = stateTyped
            const popperStyles = styles.popper
            const viewportWidth = window.innerWidth

            if (
              !(
                placement.startsWith('left') || placement.startsWith('right')
              ) ||
              !popperStyles
            )
              return

            const left = parseFloat(popperStyles.left ?? '0')

            // Clamp left so popover never overflows right edge
            if (left + popper.width > viewportWidth - 8) {
              popperStyles.left = `${viewportWidth - popper.width - 8}px`
            }
            // Clamp left to minimum 8px from left edge
            if (left < 8) {
              popperStyles.left = '8px'
            }
            // If popover is still too wide, force width to fit viewport
            if (popper.width > viewportWidth - 16) {
              popperStyles.width = `${viewportWidth - 16}px`
            }
          }
        }
      ]
    }
  }, [activeShiftPopover, daysArray.length, currentCompany?.payrollStartingDay])

  // Function to determine if popover should be shown on the left
  // Parity with PayrollOld: use getDayOfWeekIndex logic for popover placement
  const shouldShowPopoverOnLeft = (shiftId: string | null): boolean => {
    if (!shiftId) return false
    const lastDashIndex = shiftId.lastIndexOf('-')
    const secondLastDashIndex = shiftId.lastIndexOf('-', lastDashIndex - 1)
    if (lastDashIndex === -1 || secondLastDashIndex === -1) {
      return false
    }
    const dayIndex = Number(
      shiftId.substring(secondLastDashIndex + 1, lastDashIndex)
    )
    // Defensive: clamp dayIndex to valid range
    let safeDayIndex = dayIndex
    if (
      isNaN(safeDayIndex) ||
      safeDayIndex < 0 ||
      safeDayIndex >= daysArray.length
    ) {
      safeDayIndex = 0
    }
    let payrollStartingDay = currentCompany?.payrollStartingDay || 'Monday'
    const weekDays = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ]
    let startIdx = weekDays.findIndex(
      d => d.toLowerCase() === payrollStartingDay.toLowerCase()
    )
    if (startIdx === -1) startIdx = 0
    const columnPosition = (safeDayIndex + startIdx) % 7
    const showLeft = columnPosition > 2
    return showLeft
  }

  return (
    <>
      <FilterDrawer
        show={showFilterDrawer}
        drawerRef={drawerRef}
        departmentRoles={departmentRoles}
        filterState={roleFilterState}
        onFilterChange={onRoleFilterChange}
        isSingleRole={isSingleRole}
        singleRoleInfo={singleRoleInfo}
        onSwitchToRoles={() => setDisplayBy('role')}
        currentDisplayBy={displayBy}
      />

      <TableBlockStyled>
        <TableHeaderStyled>
          <OptionBlockStyled>
            <TotalEmployeesStyled>
              {filteredEmployees.length} {I18n.t('common.employees')}
            </TotalEmployeesStyled>
            <InputWrapStyled>
              <InputStyled
                type='search'
                name='Employee search'
                placeholder={I18n.t('employees.searchEmployee') + '...'}
                onChange={e => onSearchEmployee(e.target.value)}
                value={searchEmployee}
              />
              <SearchIconStyled />
            </InputWrapStyled>
            <WeekPeriodTabsStyled role='tablist'>
              {displayByArray.map(tab => (
                <TabButtonStyled
                  key={tab.id}
                  role='tab'
                  aria-selected={displayBy === tab.id}
                  onClick={() => setDisplayBy(tab.id)}
                  $isActive={displayBy === tab.id}
                >
                  {tab.icon}
                  {tab.label}
                </TabButtonStyled>
              ))}
            </WeekPeriodTabsStyled>
            <FilterButtonStyled
              ref={filterButtonRef}
              onClick={() => {
                if (displayBy === 'role') {
                  setShowFilterDrawer(!showFilterDrawer)
                } else {
                  // Switch to "Roles" view AND open FilterDrawer when clicking filter in "Employees" view
                  setDisplayBy('role')
                  setShowFilterDrawer(true)
                }
              }}
            >
              <FilterIconStyled />
              {I18n.t('payroll.filter')}
            </FilterButtonStyled>
          </OptionBlockStyled>
          <SummaryBlockStyled>
            <TotalHourStyled>
              <ClockIconStyled />
              <span>{totalHours === 0 ? 0 : totalHours.toFixed(1)}</span>{' '}
              {I18n.t('payroll.total_hours')}
            </TotalHourStyled>
            <TotalSalaryStyled>
              <span>
                <NumberFormatted value={totalSalary} />
              </span>
              {I18n.t('payroll.salaries')}
            </TotalSalaryStyled>
          </SummaryBlockStyled>
        </TableHeaderStyled>

        <TableStyled>
          {displayBy === 'role' ? (
            <>
              {Object.entries(filteredEmployeesByRole)
                .sort(([roleIdA, roleDataA], [roleIdB, roleDataB]) => {
                  // Sort by role priority (same as RolesList.js logic)
                  const priorityA = roleDataA.role.priority || 0
                  const priorityB = roleDataB.role.priority || 0
                  return priorityA - priorityB
                })
                .map(([roleId, roleData]) => (
                  <TableRowStyled key={roleId}>
                    <HeaderStyled
                      onClick={() =>
                        setExpandedRows(prev => ({
                          ...prev,
                          [roleId]: !prev[roleId]
                        }))
                      }
                    >
                      <HeaderTitleStyled>
                        {roleData.role.name}{' '}
                        <span>{roleData.employees.length}</span>
                      </HeaderTitleStyled>
                      <ArrowDownIconStyled
                        src={arrowDownIcon}
                        alt=''
                        $isExpanded={expandedRows[roleId]}
                      />
                    </HeaderStyled>
                    <BodyStyled $isExpanded={expandedRows[roleId]}>
                      {roleData.employees.map((employee: IEmployee) => {
                        // Priority logic for employee number display:
                        // 1. Employee ID (matricule) from payroll system - takes priority
                        // 2. Employee number for web time clock - fallback if no matricule or no payroll integration
                        const employeeNumber =
                          hasPayrollIntegration && employee.payrollId
                            ? employee.payrollId // Priority 1: Employee ID (matricule)
                            : employee.customId ||
                              employee.uid?.slice(-4) ||
                              '0000' // Priority 2: Employee number

                        // Remove employee-level border colors - colors should only appear on individual shift cards
                        // const employeeStatus = getEmployeeOverallStatus(employee)
                        const borderColor = undefined // No border on employee card level



                        return (
                          <ItemStyled
                            key={employee.uid}
                            $borderColor={borderColor}
                            $borderWidth="5px"
                          >
                            <ItemHeaderStyled
                              role='button'
                              onClick={() =>
                                setIsEmployeeExpanded(prev => ({
                                  ...prev,
                                  [getEmployeeId(employee)]:
                                    !prev[getEmployeeId(employee)]
                                }))
                              }
                            >
                              <EmployeeInfoStyled>
                                <AvatarInitialsStyled employee={employee} />
                                <EmployeeInfoColumnStyled>
                                  <EmployeeNameStyled>
                                    {employee.name} {employee.surname}
                                  </EmployeeNameStyled>
                                  <EmployeeIdStyled>
                                    {employeeNumber}
                                  </EmployeeIdStyled>
                                </EmployeeInfoColumnStyled>
                              </EmployeeInfoStyled>

                              <EmployeeStatsStyled>
                                {(() => {
                                  const { weeklyStats } =
                                    getEmployeeWeeklyStats(
                                      getEmployeeId(employee),
                                      attendanceData,
                                      startOfPeriod,
                                      payrollLength
                                    )
                                  return weeklyStats.map((week, weekIndex) => (
                                    <EmployeeStatsBlockStyled key={weekIndex}>
                                      <EmployeeStatsBlockItemStyled>
                                        {payrollLength === 14
                                          ? `${I18n.t('common.week_shorten')}${weekIndex + 1}`
                                          : ''}
                                        <ClockIconStyled />{' '}
                                        {week.hours.toFixed(1)}
                                        {I18n.t(
                                          'common.hours_shorten'
                                        ).toLowerCase()}
                                      </EmployeeStatsBlockItemStyled>
                                      <EmployeeStatsBlockItemStyled>
                                        <NumberFormatted value={week.salary} />
                                      </EmployeeStatsBlockItemStyled>
                                    </EmployeeStatsBlockStyled>
                                  ))
                                })()}
                              </EmployeeStatsStyled>
                            </ItemHeaderStyled>
                            {!!isEmployeeExpanded[getEmployeeId(employee)] && (
                              <ItemBodyStyled $isWeekly={false}>
                                {daysArray.map((day, index) => (
                                  <DayItemStyled
                                    key={index}
                                    $isToday={index === getCurrentDayIndex}
                                  >
                                    <DayItemHeaderStyled>
                                      <DayItemTitleStyled>
                                        {day.dayName} {day.dayNumber}
                                      </DayItemTitleStyled>
                                      <AddShiftButtonStyled
                                        ref={el => {
                                          const addShiftId = `${getEmployeeId(employee)}-${index}-add`
                                          shiftRefs.current.set(addShiftId, el)
                                        }}
                                        onClick={() => {
                                          const shiftId = `${getEmployeeId(employee)}-${index}-add`
                                          setActiveShiftPopover(shiftId)
                                        }}
                                      >
                                        <PlusIconStyled />
                                      </AddShiftButtonStyled>
                                    </DayItemHeaderStyled>
                                    {(() => {
                                      const dayShifts =
                                        getEmployeeShiftsForDate(
                                          getEmployeeId(employee),
                                          day.date,
                                          attendanceData
                                        )
                                      if (dayShifts.length === 0) {
                                        return null
                                      }
                                      return (
                                        <DayItemBodyStyled>
                                          <DayItemBodyLabelBlockStyled>
                                            <LabelBlockHeadStyled>
                                              <LabelStyled>
                                                {I18n.t('payroll.start')}
                                              </LabelStyled>
                                              <LabelStyled>
                                                {I18n.t('common.end')}
                                              </LabelStyled>
                                            </LabelBlockHeadStyled>
                                            <LabelStyled>
                                              {I18n.t('payroll.break')}
                                            </LabelStyled>
                                          </DayItemBodyLabelBlockStyled>
                                          {dayShifts.map(
                                            (
                                              shift: AttendanceShift & {
                                                isClockInDifferent?: boolean
                                                isClockOutDiffrent?: boolean
                                              },
                                              shiftIndex: number
                                            ) => {
                                              const shiftId = `${getEmployeeId(employee)}-${index}-${shiftIndex}`
                                              const breakDuration =
                                                calculateBreakDuration(
                                                  shift.breaks
                                                )

                                              // Enhanced shift status analysis
                                              const shiftAnalysis = analyzeShiftStatus(
                                                shift,
                                                getEmployeeId(employee),
                                                day.date,
                                                attendanceData,
                                                currentCompany,
                                                day.isToday
                                              )
                                              const statusColor = getShiftStatusColor(shiftAnalysis.status)


                                              return (
                                                <DayItemShiftStyled
                                                  key={shiftId}
                                                >
                                                  <TimeBlockStyled
                                                    ref={el => {
                                                      shiftRefs.current.set(
                                                        shiftId,
                                                        el
                                                      )
                                                    }}
                                                    onClick={() => {
                                                      setActiveShiftPopover(
                                                        shiftId
                                                      )
                                                    }}
                                                    title={`${getShiftStatusDescription(shiftAnalysis.status)}${shiftAnalysis.issues.length > 0 ? ': ' + shiftAnalysis.issues.join(', ') : ''}`}
                                                    $isBlue={statusColor === 'blue'}
                                                    $isWhite={statusColor === 'white'}
                                                    $isGrey={statusColor === 'grey'}
                                                    $isOrange={statusColor === 'orange'}
                                                    $isRed={statusColor === 'red'}
                                                    style={{
                                                      backgroundColor: statusColor === 'red' ? '#FFE6E6' : undefined,
                                                      boxShadow: statusColor === 'red' ? '0 0 10px #FF0000' : undefined
                                                    }}
                                                  >
                                                    <TimeBlockValueStyled>
                                                      {formatTime(shift.start)}
                                                    </TimeBlockValueStyled>
                                                    <TimeBlockValueStyled>
                                                      {formatTime(shift.end)}
                                                    </TimeBlockValueStyled>
                                                  </TimeBlockStyled>
                                                  <TimeBlockBreakValueStyled>
                                                    {breakDuration > 0
                                                      ? Math.round(
                                                          breakDuration
                                                        )
                                                      : '--'}{' '}
                                                    {breakDuration > 0
                                                      ? I18n.t(
                                                          'common.minutes_shorten'
                                                        ).toLowerCase()
                                                      : ''}
                                                  </TimeBlockBreakValueStyled>
                                                </DayItemShiftStyled>
                                              )
                                            }
                                          )}
                                        </DayItemBodyStyled>
                                      )
                                    })()}
                                  </DayItemStyled>
                                ))}
                              </ItemBodyStyled>
                            )}
                          </ItemStyled>
                        )
                      })}
                    </BodyStyled>
                  </TableRowStyled>
                )
              )}
            </>
          ) : (
            <TableRowStyled>
              <BodyStyled
                $isExpanded={true}
                $noBorder
              >
                {filteredEmployees.map(employee => {
                  // Remove employee-level border colors - colors should only appear on individual shift cards
                  // const employeeStatus = getEmployeeOverallStatus(employee)
                  const borderColor = undefined // No border on employee card level



                  return (
                    <ItemStyled
                      key={employee.id}
                      $borderColor={borderColor}
                      $borderWidth="5px"
                    >
                    <ItemHeaderStyled
                      role='button'
                      onClick={() =>
                        setIsEmployeeExpanded(prev => ({
                          ...prev,
                          [getEmployeeId(employee)]:
                            !prev[getEmployeeId(employee)]
                        }))
                      }
                    >
                      <EmployeeInfoStyled>
                        <AvatarInitialsStyled
                          employee={employee as unknown as IEmployee}
                        />
                        <EmployeeInfoColumnStyled>
                          <EmployeeNameStyled>
                            {employee.name} {employee.surname}
                          </EmployeeNameStyled>
                          <EmployeeIdStyled>
                            {employee.payrollId ||
                              employee.customId ||
                              employee.uid?.slice(-4) ||
                              '0000'}
                          </EmployeeIdStyled>
                        </EmployeeInfoColumnStyled>
                      </EmployeeInfoStyled>

                      <EmployeeStatsStyled>
                        {(() => {
                          const { weeklyStats } = getEmployeeWeeklyStats(
                            employee.uid || '',
                            attendanceData,
                            startOfPeriod,
                            payrollLength
                          )
                          return weeklyStats.map((week, weekIndex) => (
                            <EmployeeStatsBlockStyled key={weekIndex}>
                              <EmployeeStatsBlockItemStyled>
                                {payrollLength === 14
                                  ? `${I18n.t('common.week_shorten')}${weekIndex + 1}`
                                  : ''}
                                <ClockIconStyled /> {week.hours.toFixed(1)}
                                {I18n.t('common.hours_shorten').toLowerCase()}
                              </EmployeeStatsBlockItemStyled>
                              <EmployeeStatsBlockItemStyled>
                                $ {week.salary.toFixed(2)}
                              </EmployeeStatsBlockItemStyled>
                            </EmployeeStatsBlockStyled>
                          ))
                        })()}
                      </EmployeeStatsStyled>
                    </ItemHeaderStyled>
                    {isEmployeeExpanded[getEmployeeId(employee)] && (
                      <ItemBodyStyled $isWeekly={false}>
                        {daysArray.map((day, index) => (
                          <DayItemStyled
                            key={index}
                            $isToday={index === getCurrentDayIndex}
                          >
                            <DayItemHeaderStyled>
                              <DayItemTitleStyled>
                                {day.dayName} {day.dayNumber}
                              </DayItemTitleStyled>
                              <AddShiftButtonStyled
                                ref={el => {
                                  const addShiftId = `${getEmployeeId(employee)}-${index}-add`
                                  shiftRefs.current.set(addShiftId, el)
                                }}
                                onClick={() => {
                                  const shiftId = `${getEmployeeId(employee)}-${index}-add`
                                  setActiveShiftPopover(shiftId)
                                }}
                              >
                                <PlusIconStyled />
                              </AddShiftButtonStyled>
                            </DayItemHeaderStyled>
                            {(() => {
                              const dayShifts = getEmployeeShiftsForDate(
                                getEmployeeId(employee),
                                day.date,
                                attendanceData
                              )
                              if (dayShifts.length === 0) {
                                return null
                              }
                              return (
                                <DayItemBodyStyled>
                                  <DayItemBodyLabelBlockStyled>
                                    <LabelBlockHeadStyled>
                                      <LabelStyled>
                                        {I18n.t('payroll.start')}
                                      </LabelStyled>
                                      <LabelStyled>
                                        {I18n.t('common.end')}
                                      </LabelStyled>
                                    </LabelBlockHeadStyled>
                                    <LabelStyled>
                                      {I18n.t('payroll.break')}
                                    </LabelStyled>
                                  </DayItemBodyLabelBlockStyled>
                                  {dayShifts.map(
                                    (
                                      shift: AttendanceShift & {
                                        isClockInDifferent?: boolean
                                        isClockOutDiffrent?: boolean
                                      },
                                      shiftIndex: number
                                    ) => {
                                      const shiftId = `${getEmployeeId(employee)}-${index}-${shiftIndex}`
                                      const breakDuration =
                                        calculateBreakDuration(shift.breaks)

                                      // Enhanced shift status analysis
                                      const shiftAnalysis = analyzeShiftStatus(
                                        shift,
                                        getEmployeeId(employee),
                                        day.date,
                                        attendanceData,
                                        currentCompany,
                                        day.isToday
                                      )
                                      const statusColor = getShiftStatusColor(shiftAnalysis.status)

                                      // Debug logging to verify status and color mapping
                                      console.log('🎨 Shift Status Debug:', {
                                        employeeId: getEmployeeId(employee),
                                        date: day.date,
                                        shiftIndex,
                                        status: shiftAnalysis.status,
                                        statusColor,
                                        issues: shiftAnalysis.issues,
                                        priority: shiftAnalysis.priority
                                      })

                                      return (
                                        <DayItemShiftStyled
                                          key={shiftId}
                                          $isBlue={statusColor === 'blue'}
                                          $isGrey={statusColor === 'grey'}
                                          $isOrange={statusColor === 'orange'}
                                          $isRed={statusColor === 'red'}
                                        >
                                          <TimeBlockStyled
                                            ref={el => {
                                              shiftRefs.current.set(shiftId, el)
                                            }}
                                            onClick={() => {
                                              setActiveShiftPopover(shiftId)
                                            }}
                                            title={`${getShiftStatusDescription(shiftAnalysis.status)}${shiftAnalysis.issues.length > 0 ? ': ' + shiftAnalysis.issues.join(', ') : ''}`}
                                          >
                                            <TimeBlockValueStyled>
                                              {formatTime(shift.start)}
                                            </TimeBlockValueStyled>
                                            <TimeBlockValueStyled>
                                              {formatTime(shift.end)}
                                            </TimeBlockValueStyled>
                                          </TimeBlockStyled>
                                          <TimeBlockBreakValueStyled>
                                            {breakDuration > 0
                                              ? Math.round(breakDuration)
                                              : '--'}{' '}
                                            {breakDuration > 0
                                              ? I18n.t(
                                                  'common.minutes_shorten'
                                                ).toLowerCase()
                                              : ''}
                                          </TimeBlockBreakValueStyled>
                                        </DayItemShiftStyled>
                                      )
                                    }
                                  )}
                                </DayItemBodyStyled>
                              )
                            })()}
                          </DayItemStyled>
                        ))}
                      </ItemBodyStyled>
                    )}
                  </ItemStyled>
                  )
                })}
              </BodyStyled>
            </TableRowStyled>
          )}
        </TableStyled>
      </TableBlockStyled>
      <Overlay
        rootClose
        show={activeShiftPopover !== null}
        target={() =>
          activeShiftPopover
            ? (shiftRefs.current.get(activeShiftPopover) ?? null)
            : null
        }
        placement={
          shouldShowPopoverOnLeft(activeShiftPopover) ? 'left' : 'right'
        }
        onHide={() => {
          const activitiesPopover = document.getElementById(
            'payroll_activities-popover'
          )
          if (activitiesPopover) {
            return
          }
          setActiveShiftPopover(null)
        }}
        // TODO Lie - ?
        container={undefined}
        // TODO Lie - ?
        flip={true}
        popperConfig={refinedPopperConfig}
      >
        {activeShiftInfo ? (
          <ShiftPopover
            onClose={() => setActiveShiftPopover(null)}
            showActivityPopoverOnLeft={shouldShowPopoverOnLeft(
              activeShiftPopover
            )}
            employee={activeShiftInfo.employee as unknown as IEmployee}
            date={activeShiftInfo.date}
            shifts={activeShiftInfo.shifts || []}
            isNewShift={activeShiftInfo.isNewShift || false}
            currentCompany={currentCompany}
            attendanceData={attendanceData}
            onSave={(newShift: { [key: string]: AttendanceShift }) =>
              onSave(
                newShift,
                activeShiftInfo.employee.uid,
                activeShiftInfo.date
              )
            }
            onDeleteShift={(shiftKey: string) =>
              onDeleteShift(
                shiftKey,
                activeShiftInfo.employee.uid,
                activeShiftInfo.date
              )
            }
          />
        ) : (
          <div
            style={{
              padding: '1rem',
              textAlign: 'center',
              backgroundColor: 'white',
              border: '1px solid #ccc',
              borderRadius: '8px'
            }}
          >
            Loading shift details...
          </div>
        )}
      </Overlay>
    </>
  )
}

export default HoursTable

const TabListStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: center;

  padding: 0.3rem;
  gap: 0.3rem;

  border-radius: 0.8rem;
  background-color: rgba(229, 235, 239, 0.8);
`

const TabButtonStyled = styled.button<{
  $isActive: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;
  padding: 0.3rem 1rem;
  min-width: 7.5rem;

  border: none;
  box-shadow: ${({ $isActive }) =>
    $isActive
      ? '2px 2px 4px -1 rgba(18, 18, 23,0.06),2px 2px 4px -1 rgba(18, 18, 23,0.08)'
      : null};
  background: ${({ $isActive }) =>
    $isActive ? 'linear-gradient(180deg, #3BBCFF, #2D87FF 100%)' : 'none'};
  border-radius: 0.6rem;

  color: ${({ $isActive }) =>
    $isActive ? 'white' : theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  font-family: ${theme.fonts.normal};
  transition: all 0.2s ease-in-out;
  opacity: ${({ $isActive }) => ($isActive ? 1 : 0.3)};

  &:hover,
  &:focus {
    opacity: 1;
  }

  svg {
    width: 1.2rem;
    height: 1.2rem;
    flex-shrink: 0;
    fill: currentColor;
  }
`

const WeekPeriodTabsStyled = styled(TabListStyled)`
  padding: 0;

  ${TabButtonStyled} {
    min-width: 5.5rem;
    font-size: 0.875rem;
  }
`

const ClockIconStyled = styled(ClockIcon)`
  width: 1rem;
  height: 1rem;

  fill: currentColor;
`

const TableBlockStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.5rem;
  flex: 1;
  width: 100%;

  margin-top: 1.5rem;
  min-height: 0;
`

const TableHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding-inline: 1.5rem;
`

const OptionBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 1rem;
`

const TotalEmployeesStyled = styled.div`
  min-width: 8rem;

  text-align: center;
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  color: ${theme.colorsNew.darkGrey500};
  text-transform: lowercase;
`

const InputWrapStyled = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`

const InputStyled = styled(Input)`
  width: 100%;
  height: 2rem;
  padding-left: 2.4rem;

  border: 1px solid #caced4;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};

  :hover,
  :focus {
    background-color: #fff;
    box-shadow: 0px 2px 4px -1 rgba(18, 18, 23, 0.06);
  }
  ::placeholder {
    color: rgba(69, 84, 104, 0.8);
    font-family: ${theme.fonts.normal};
  }
`

const SearchIconStyled = styled(SearchIcon)`
  width: 0.9rem;
  height: 0.9rem;

  position: absolute;
  left: 0.8rem;
  pointer-events: none;
  fill: currentColor;
`

const FilterButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.3rem;
  padding: 0.2rem 0.6rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};

  transition: all 0.2s ease-in-out;
  :hover,
  :focus {
    color: #00a2e9;
  }
`

const FilterIconStyled = styled(FilterIcon)`
  width: 1rem;
  height: 1rem;
  fill: currentColor;
`

const SummaryBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`

const TotalHourStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5ch;
  padding: 0.1rem 0.6rem;

  border-radius: 0.6rem;
  background-color: #cfd9ff;

  font-size: 0.8rem;
  color: ${theme.colorsNew.darkGrey500};
  font-family: ${theme.fonts.normal};

  span {
    font-family: ${theme.fonts.bold};
  }
`

const TotalSalaryStyled = styled(TotalHourStyled)`
  background-color: #daf1dd;
`

const TableStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.5rem;
  flex: 1;
  width: 100%;
  padding: 0 1rem 1rem;

  overflow-y: auto;
`

const TableRowStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  background-color: #fff;
  border-radius: 0.8rem;
`

const HeaderStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  padding: 1rem 1.5rem;

  border: 0;
  background-color: transparent;
`
const ArrowDownIconStyled = styled.img<{ $isExpanded: boolean }>`
  width: 1rem;
  height: 1rem;
  transform: ${({ $isExpanded }) => ($isExpanded ? 'rotate(180deg)' : null)};
  transition: all 0.1s ease-in-out;
`
const HeaderTitleStyled = styled.p`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  span {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    width: 1.2rem;
    height: 1.2rem;

    border-radius: 50%;
    background-color: rgba(28, 34, 43, 0.05);

    color: ${theme.colorsNew.darkGrey500};
    font-size: 0.7rem;
    font-family: ${theme.fonts.normal};
    line-height: normal;
  }
`

const BodyStyled = styled.div<{ $isExpanded: boolean; $noBorder?: boolean }>`
  display: flex;
  flex-direction: column;

  padding: 0 1.5rem;
  width: 100%;
  height: ${({ $isExpanded }) => ($isExpanded ? 'auto' : '0')};
  transform: ${({ $isExpanded }) =>
    $isExpanded ? 'translateY(0)' : 'translateY(-1rem)'};

  border-top: ${({ $noBorder }) => ($noBorder ? '0' : '1px solid #e7e7e7')};

  overflow: hidden;
  opacity: ${({ $isExpanded }) => ($isExpanded ? '1' : '0')};
  visibility: ${({ $isExpanded }) => ($isExpanded ? 'visible' : 'hidden')};
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
`

const ItemStyled = styled.div<{
  $borderColor?: string
  $borderWidth?: string
}>`
  display: flex;
  flex-direction: column;

  width: 100%;
  padding: 0.75rem 0;
  border-left: ${({ $borderWidth, $borderColor }) =>
    $borderColor ? `${$borderWidth || '5px'} solid ${$borderColor}` : 'none'};
  border-radius: ${({ $borderColor }) => ($borderColor ? '0 0.5rem 0.5rem 0' : '0')};
  :not(:last-of-type) {
    border-bottom: 1px solid #e7e7e7;
  }
`

const ItemHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
`

const EmployeeInfoStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.5rem;
  flex: 1;
`

const AvatarInitialsStyled = styled(AvatarInitials)`
  width: 2rem;
  height: 2rem;

  font-size: 0.8rem;
`

const EmployeeInfoColumnStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
`

const EmployeeNameStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`
const EmployeeIdStyled = styled.span`
  color: #afbaca;
  font-size: 0.7rem;
  font-family: ${theme.fonts.normal};
  line-height: 1;
`

const EmployeeStatsStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 2rem;
`

const EmployeeStatsBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const EmployeeStatsBlockItemStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.2rem;
  padding: 0.15rem 0.6rem;

  border-radius: 0.6rem;
  background-color: rgba(214, 223, 255, 0.5);

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.8rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  :last-of-type {
    background-color: #e8f6eb;
  }
`

const ItemBodyStyled = styled.div<{ $isWeekly: boolean }>`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: ${({ $isWeekly }) =>
    $isWeekly ? 'repeat(2, 1fr)' : null};

  padding: 0.5rem 0;
  margin-top: 0.5rem;
  grid-row-gap: 1.5rem;
  grid-column-gap: 0.45rem;
`

const DayItemStyled = styled.div<{ $isToday: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  width: 100%;

  border-radius: 0.6rem;
  background-color: rgba(239, 239, 242, 0.8);

  border: 2px solid ${({ $isToday }) => ($isToday ? '#00a2e9' : 'transparent')};
`

const DayItemHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: 0.5rem 1rem;
  position: relative;
`

const DayItemTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.925rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const AddShiftButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.3rem;
  height: 1.3rem;
  padding: 0;

  position: absolute;
  right: 0.5rem;

  border: 0;
  border-radius: 50%;
  background-color: #fff;

  color: #bfc0c7;

  :hover,
  :focus {
    color: ${theme.colorsNew.darkGrey500};
    background-color: #d6dfff;
  }
`

const PlusIconStyled = styled(PlusIcon)`
  width: 0.8rem;
  height: 0.8rem;
  fill: currentColor;
`

const DayItemBodyStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 0.2rem;
  width: 100%;
  padding: 0.5rem 0.8rem;
`

const DayItemBodyLabelBlockStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  width: 100%;
  grid-template-columns: 1.5fr 1fr;
`

const LabelStyled = styled.p`
  flex: 1;
  text-align: center;
  color: #989eae;
  font-size: 0.75rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const LabelBlockHeadStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;
  padding: 0.2rem 0.5rem;
`

// Helper function to determine border color based on shift status
const getBorderColor = (props: {
  $borderColor?: string
  $isBlue?: boolean
  $isRed?: boolean
  $isOrange?: boolean
  $isGrey?: boolean
}) => {
  // Debug logging for border color determination
  const color = props.$borderColor ? props.$borderColor :
    props.$isBlue ? '#32ADE6' : // Currently working (blue)
    props.$isRed ? '#FF0000' : // Overlapping shifts - urgent (red)
    props.$isOrange ? '#FF9500' : // Problematic - needs manager attention (orange)
    props.$isGrey ? '#9CA3AF' : // Different role (grey)
    '#FFFFFF' // Approved shift (white)

  console.log('🎨 getBorderColor:', {
    props: {
      $borderColor: props.$borderColor,
      $isBlue: props.$isBlue,
      $isRed: props.$isRed,
      $isOrange: props.$isOrange,
      $isGrey: props.$isGrey
    },
    color
  })
  return color
}

const TimeBlockStyled = styled.button.attrs<{
  $hasOpacity?: boolean
  $isBlue?: boolean
  $isWhite?: boolean
  $isGrey?: boolean
  $isOrange?: boolean
  $isRed?: boolean
  $borderColor?: string
  $borderWidth?: string
}>(props => {
  return {}
})<{
  $hasOpacity?: boolean
  $isBlue?: boolean
  $isWhite?: boolean
  $isGrey?: boolean
  $isOrange?: boolean
  $isRed?: boolean
  $borderColor?: string
  $borderWidth?: string
}>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;
  padding: 0.2rem 0.5rem;

  border: 1px solid #e0e0e0;
  border-radius: 0.8rem;
  background-color: #FFFFFF;
  opacity: ${({ $hasOpacity }) => ($hasOpacity ? 0.4 : 1)};
  :hover {
    background-color: #d6dfff;
    /* Don't override border-color on hover - keep the status color */
  }
`

const DayItemShiftStyled = styled.div<{
  $isBlue?: boolean
  $isRed?: boolean
  $isOrange?: boolean
  $isGrey?: boolean
  $borderWidth?: string
}>`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  width: 100%;
  grid-template-columns: 1.5fr 1fr;

  /* Shift status border styling */
  border: ${({ $borderWidth, ...props }) =>
    `${$borderWidth || '3px'} solid ${getBorderColor(props)} !important`};
  border-radius: 0.8rem;
  padding: 0.5rem;
  margin: 0.25rem 0;
  box-shadow: ${({ $isRed }) => $isRed ? '0 0 10px red' : 'none'};
  background-color: ${({ $isRed, $isOrange, $isBlue, $isGrey }) =>
    $isRed ? '#FFE6E6' :
    $isOrange ? '#FFF4E6' :
    $isBlue ? '#E6F3FF' :
    $isGrey ? '#F3F4F6' : '#FFFFFF'};
`
const TimeBlockValueStyled = styled.p`
  text-align: center;
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.775rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const TimeBlockBreakValueStyled = styled(TimeBlockValueStyled)<{
  $hasOpacity?: boolean
}>`
  opacity: ${({ $hasOpacity }) => ($hasOpacity ? 0.4 : 1)};
  color: #989eae;
`
