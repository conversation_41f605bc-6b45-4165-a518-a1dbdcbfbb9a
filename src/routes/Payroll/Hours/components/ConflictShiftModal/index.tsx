import React, { useRef, useState } from 'react'
import <PERSON><PERSON> from 'react-bootstrap/Modal'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import ShiftCard from './ShiftCard'

import checkIcon from 'img/icons/checkBlueIcon.svg'
import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'
import flagIcon from 'img/icons/flagIcon.svg'

const ConflictShiftModal = ({
  show,
  onHide
}: {
  show: boolean
  onHide: () => void
}) => {
  const [activeType, setActiveType] = useState('clocked-in-early')
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const typeRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  const typeList = [
    {
      id: 'clocked-in-early',
      title: I18n.t('payroll.clocked_in_early'),
      count: 1
    },
    {
      id: 'missing-end',
      title: I18n.t('payroll.missing_end'),
      count: 1
    },
    {
      id: 'clocked-out-late',
      title: I18n.t('payroll.clocked_out_late'),
      count: 0
    },
    {
      id: 'unplanned-shift',
      title: I18n.t('payroll.unplanned_shift'),
      count: 0
    },
    {
      id: 'shift-too-short',
      title: I18n.t('payroll.shift_too_short'),
      count: 0
    },
    {
      id: 'shift-under-3-hours',
      title: I18n.t('payroll.shift_under_3_hours'),
      count: 0
    }
  ]

  const [activePeriod, setActivePeriod] = useState('entire-period')
  const periodList = [
    {
      id: 'entire-period',
      title: I18n.t('payroll.entire_period'),
      count: 8
    },
    {
      id: 'week-1',
      title: I18n.t('payroll.week') + ' 1',
      count: 3
    },
    {
      id: 'week-2',
      title: I18n.t('payroll.week') + ' 2',
      count: 0
    },
    {
      id: 'today',
      title: I18n.t('payroll.today'),
      count: 0
    }
  ]

  const conflictItems: string[] = ['1', '2', '3', '4']

  const handleTypeClick = (typeId: string) => {
    setActiveType(typeId)
    const targetRef = typeRefs.current[typeId]
    if (targetRef && scrollContainerRef.current) {
      targetRef.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  const setTypeRef = (typeId: string) => (el: HTMLDivElement | null) => {
    typeRefs.current[typeId] = el
  }

  return (
    <ModalStyled
      show={show}
      onHide={onHide}
    >
      <HeaderStyled>
        <TitleStyled>
          <img
            src={flagIcon}
            alt=''
          />
          {I18n.t('payroll.conflicting_shifts')}
        </TitleStyled>
        <SubtitleStyled>
          {I18n.t(
            'payroll.please_check_the_time_cards_below_to_ensure_proper_payroll_management'
          )}
        </SubtitleStyled>
        <CloseButtonStyled onClick={onHide}>
          <CloseIconStyled />
        </CloseButtonStyled>
      </HeaderStyled>
      <BodyStyled>
        <ListStyled>
          {typeList.map(item => (
            <ListItemStyled
              $isActive={item.id === activeType}
              onClick={() => handleTypeClick(item.id)}
              key={item.id}
            >
              {item.title}
              {item.count > 0 && <span>{item.count}</span>}
            </ListItemStyled>
          ))}
        </ListStyled>
        <BlockStyled>
          <PeriodWrapStyled>
            <PeriodTabsStyled>
              {/* Sidebar is ordered by priority:
Clocked in Early
Missing End
Unplanned shift
Shift under 3hrs
Shift too short
Clocked out late
Unless the category has no conflicts, in which case it will be moved to the bottom of the list
*/}
              {periodList.map(item => (
                <PeriodItemStyled
                  $isActive={item.id === activePeriod}
                  onClick={() => setActivePeriod(item.id)}
                  key={item.id}
                >
                  {item.title} {item.count > 0 && <span>{item.count}</span>}
                </PeriodItemStyled>
              ))}
            </PeriodTabsStyled>
          </PeriodWrapStyled>
          <ScrollContainerStyled ref={scrollContainerRef}>
            {conflictItems.length > 0 ? (
              typeList.map(type => (
                <TypeSectionStyled
                  key={type.id}
                  ref={setTypeRef(type.id)}
                >
                  <ShiftCard
                    activeType={type.id}
                    conflictItems={conflictItems}
                  />
                </TypeSectionStyled>
              ))
            ) : (
              <CardEmptyStyled>
                <img
                  src={checkIcon}
                  alt=''
                />
                {I18n.t('payroll.you_are_all_caught_up')}
              </CardEmptyStyled>
            )}
          </ScrollContainerStyled>
        </BlockStyled>
      </BodyStyled>
    </ModalStyled>
  )
}

export default ConflictShiftModal

const ModalStyled = styled(Modal)`
  .modal-content {
    width: 70vw;
    min-width: 30rem;
  }
`

const HeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0.8rem 1.5rem;

  border-bottom: 1px solid rgba(10, 12, 17, 0.1);
`

const TitleStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.6rem;

  color: #0a0c11;
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
`

const SubtitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.light};
`

const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.6rem;

  border: none;
  border-radius: 50%;
  background: rgba(28, 34, 43, 0.04);
  color: ${theme.colorsNew.darkGrey500};

  :hover,
  :focus {
    background: rgba(28, 34, 43, 0.1);
  }
`

const CloseIconStyled = styled(CloseIcon)`
  width: 0.8rem;
  height: 0.8rem;
  stroke: currentColor;
`

const BodyStyled = styled.div`
  display: flex;
`

const ListStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.2rem;
  flex: 1;
  padding: 0.8rem 1rem;

  border-right: 1px solid rgba(10, 12, 17, 0.1);
`

const ListItemStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0.5rem 0.8rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: ${({ $isActive }) => ($isActive ? '#EDF2F7' : '#fff')};

  color: ${({ $isActive }) =>
    $isActive ? theme.colorsNew.darkGrey500 : 'rgba(69, 84, 104, 0.6)'};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};

  span {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 1rem;
    height: 1rem;

    border-radius: 50%;
    background-color: #667085;

    color: #fff;
    font-size: 0.65rem;
    font-family: ${theme.fonts.bold};
    line-height: normal;
  }

  :hover,
  :focus {
    color: ${theme.colorsNew.darkGrey500};
    background-color: #edf2f7;
  }
`

const BlockStyled = styled.div`
  display: flex;
  flex-direction: column;

  flex: 4.5;

  background-color: #edf2f7;
  border-bottom-right-radius: 0.8rem;
`

const PeriodWrapStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 1.5rem;
`

const PeriodTabsStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.6rem;
  width: 100%;
`

const PeriodItemStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.6rem;
  flex: 1;
  padding: 0.4rem 1rem;

  border: 1px solid
    ${({ $isActive }) =>
      $isActive ? 'rgba(69, 84, 104, 0.05)' : 'transparent'};
  border-radius: 0.6rem;
  background-color: ${({ $isActive }) => ($isActive ? '#32ADE6' : '#fff')};

  color: ${({ $isActive }) =>
    $isActive ? '#fff' : theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  span {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 0.9rem;
    height: 0.9rem;

    border-radius: 50%;
    background-color: ${({ $isActive }) => ($isActive ? '#fff' : '#667085')};

    color: ${({ $isActive }) => ($isActive ? '#32ADE6' : '#fff')};
    font-size: 0.65rem;
    font-family: ${theme.fonts.normal};
    line-height: normal;
  }

  :hover,
  :focus {
    background-color: ${({ $isActive }) =>
      $isActive ? '#32ade6' : 'rgba(69, 84, 104, 0.1)'};
  }
`

const ScrollContainerStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  gap: 1.5rem;
  min-height: 25rem;
  max-height: 60vh;
  padding: 0 1.5rem 1.5rem;

  overflow-y: auto;
`

const TypeSectionStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  width: 100%;
`

const CardEmptyStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  gap: 0;
  flex: 1;
  width: 100%;
  padding: 1.5rem;

  border-radius: 0.8rem;
  background-color: rgba(255, 255, 255, 0.6);

  color: #32ade6;
  font-size: 1.1rem;
  font-family: ${theme.fonts.normal};

  img {
    width: 1.5rem;
    height: 1.5rem;
  }
`
