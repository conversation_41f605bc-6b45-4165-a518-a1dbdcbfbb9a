import React, { useState } from 'react'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import Popover from 'react-bootstrap/Popover'
import { I18n } from 'react-redux-i18n'
import { toast } from 'react-toastify'

import { Moment } from 'moment'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { AvatarInitials } from 'components/ui/AvatarInitials'
import NumberFormatted from 'components/ui/NumberFormatted'
import { OutlineButton } from 'components/ui/OutlineButton'
import CustomSelect from 'components/ui/Select'
import CustomTimePicker from 'components/ui/TimePicker'

import DeleteShiftModal from '../DeleteShiftModal'
import PayrollToaster from '../PayrollToaster'

import checkIcon from 'img/icons/checkBlueIcon.svg'
import { ReactComponent as WarningIcon } from 'img/icons/exclamationFilled.svg'
import { ReactComponent as DeleteIcon } from 'img/icons/trashNewIcon.svg'

const CONFLICT_TYPE_CONFIG = {
  'clocked-in-early': {
    title: I18n.t('payroll.clocked_in_early'),
    subtitle: I18n.t('payroll.the_employee_clocked_in_earlier_than_planned')
  },
  'missing-end': {
    title: I18n.t('payroll.missing_end'),
    subtitle: I18n.t('payroll.the_employee_did_not_clock_out_of_their_shift')
  },
  'clocked-out-late': {
    title: I18n.t('payroll.clocked_out_late'),
    subtitle: I18n.t('payroll.the_employee_clocked_out_later_than_planned')
  },
  'unplanned-shift': {
    title: I18n.t('payroll.unplanned_shift'),
    subtitle: I18n.t(
      'payroll.the_clocked_shift_was_not_planned_for_this_employee'
    )
  },
  'shift-too-short': {
    title: I18n.t('payroll.shift_too_short'),
    subtitle: I18n.t('payroll.shift_was_shorter_than_expected')
  },
  'shift-under-3-hours': {
    title: I18n.t('payroll.shift_under_3_hours'),
    subtitle: I18n.t('payroll.shift_was_under_3_hours')
  }
} as const

const ShiftCard = ({
  activeType,
  conflictItems
}: {
  activeType: string
  conflictItems: string[]
}) => {
  const options = [
    {
      label: 'Waiter',
      value: 'waiter'
    },
    {
      label: 'Cook',
      value: 'cook'
    }
  ]
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const [shiftTimeStart, setShiftTimeStart] = useState<Moment | null>(null)
  const [shiftTimeEnd, setShiftTimeEnd] = useState<Moment | null>(null)
  const [hasDecision, setHasDecision] = useState(false)
  const [hasClaimed, setHasClaimed] = useState(false)
  const { title, subtitle } =
    CONFLICT_TYPE_CONFIG[activeType as keyof typeof CONFLICT_TYPE_CONFIG]

  const hidePlannedRow =
    activeType === 'unplanned-shift' || activeType === 'missing-end'

  const isUnplannedShift = activeType === 'unplanned-shift'
  const canReclaim = activeType === 'clocked-in-early'

  const [showDeleteShiftModal, setShowDeleteShiftModal] = useState(false)

  const saveAmount = 5.42

  const onChangeStatus = (
    status: 'approve' | 'delete' | 'claim' | 'modify',
    onUndo: () => void
  ) => {
    setHasDecision(true)
    // MUST
    // after 5 sec remove this shift from conflictItems
    toast(
      <PayrollToaster
        onUndo={onUndo}
        status={status}
      />,
      {
        className: 'payroll-toaster',
        closeButton: false,
        hideProgressBar: true,
        position: 'top-right',
        autoClose: 5000,
        pauseOnFocusLoss: false
      }
    )
  }

  return (
    <CardStyled $isEmpty={!conflictItems.length}>
      <CardHeaderStyled>
        <CardTitleStyled>{title}</CardTitleStyled>
        <CardSubtitleStyled>{subtitle}</CardSubtitleStyled>
      </CardHeaderStyled>

      {conflictItems.length > 0 ? (
        <CardListStyled>
          {conflictItems.map(item => (
            <CardItemStyled
              key={item}
              $hasOverlay={hasDecision}
              $isDeleting={showDeleteShiftModal}
            >
              {showDeleteShiftModal && (
                <DeleteShiftModal
                  onClose={() => setShowDeleteShiftModal(false)}
                  onDelete={() => {
                    onChangeStatus('delete', () => setHasDecision(false))
                    setShowDeleteShiftModal(false)
                  }}
                  isConflictModal
                />
              )}
              <DateStyled>
                Friday 10
                <WarningIconStyled />
              </DateStyled>
              <InfoBlockStyled>
                <AvatarInitialsStyled employee={undefined} />
                <InfoBlockTextStyled>John Doe</InfoBlockTextStyled>
              </InfoBlockStyled>
              <RoleBlockStyled>
                <RoleBlockLabelStyled>
                  {I18n.t('common.role')}
                </RoleBlockLabelStyled>
                <CustomSelectStyled
                  options={options}
                  value={options.find(option => option.value === selectedRole)}
                  onChange={option =>
                    option && setSelectedRole(option.value as string)
                  }
                  placeholder={I18n.t('common.select')}
                  noOptionsMessage={I18n.t('payroll.no_roles_available')}
                  components={{
                    IndicatorSeparator: null
                  }}
                  $noValue={!selectedRole}
                />
              </RoleBlockStyled>
              <ShiftBlockStyled>
                <RowStyled>
                  <RowCellStyled />
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                </RowStyled>
                {!hidePlannedRow && (
                  <RowStyled>
                    <RowCellStyled>{I18n.t('payroll.planned')}</RowCellStyled>
                    <RowCellStyled>10:00</RowCellStyled>
                    <RowCellStyled>18:00</RowCellStyled>
                  </RowStyled>
                )}
                <RowStyled>
                  <RowCellStyled>{I18n.t('common.shift')}</RowCellStyled>
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setShiftTimeStart(value)
                    }}
                    value={shiftTimeStart}
                    placeholder='-'
                    hideArrow
                    $hasConflict={true}
                  />
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setShiftTimeEnd(value)
                    }}
                    value={shiftTimeEnd}
                    placeholder='-'
                    hideArrow
                    $hasConflict={false}
                  />
                </RowStyled>
              </ShiftBlockStyled>

              <CardFooterStyled>
                <ButtonWrapStyled>
                  {isUnplannedShift && (
                    <DeleteButtonStyled
                      onClick={() => setShowDeleteShiftModal(true)}
                    >
                      <DeleteIconStyled />
                      {I18n.t('common.delete')}
                    </DeleteButtonStyled>
                  )}

                  {canReclaim && (
                    <OverlayTrigger
                      trigger={['hover', 'focus']}
                      placement='bottom-start'
                      overlay={
                        <TooltipStyled id='conflict-shift-modal_reclaim-tooltip'>
                          {I18n.t('payroll.you_save')}{' '}
                          <NumberFormatted value={saveAmount} />{' '}
                          {I18n.t('payroll.with_planned_start_time')}
                        </TooltipStyled>
                      }
                    >
                      <OrangeButtonStyled
                        color='orange'
                        onClick={() => {
                          onChangeStatus('claim', () => {
                            setHasClaimed(false)
                            setHasDecision(false)
                          })
                          setHasClaimed(!hasClaimed)
                        }}
                        $isActive={hasClaimed}
                      >
                        <p>
                          + <NumberFormatted value={saveAmount} />
                        </p>
                        <p>
                          {I18n.t('payroll.saved')}{' '}
                          <NumberFormatted value={saveAmount} />
                        </p>
                      </OrangeButtonStyled>
                    </OverlayTrigger>
                  )}
                </ButtonWrapStyled>

                <ButtonWrapStyled>
                  {isUnplannedShift ? (
                    <GreyButtonStyled
                      onClick={() =>
                        onChangeStatus('modify', () => setHasDecision(false))
                      }
                    >
                      {I18n.t('common.save')}
                    </GreyButtonStyled>
                  ) : (
                    <GreenButtonStyled
                      color='green'
                      onClick={() => {
                        if (hasDecision) {
                          setHasDecision(false)
                          setHasClaimed(false)
                        } else {
                          onChangeStatus('approve', () => setHasDecision(false))
                        }
                      }}
                      $isActive={hasDecision}
                    >
                      <span>{I18n.t('common.approve')}</span>
                      <span>{I18n.t('common.undo')}</span>
                    </GreenButtonStyled>
                  )}
                </ButtonWrapStyled>
              </CardFooterStyled>
            </CardItemStyled>
          ))}
        </CardListStyled>
      ) : (
        <CardEmptyBlockStyled>
          <img
            src={checkIcon}
            alt=''
          />
          {I18n.t('payroll.no_conflicts')}
        </CardEmptyBlockStyled>
      )}
    </CardStyled>
  )
}

export default ShiftCard

const CardStyled = styled.div<{ $isEmpty?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  gap: 0.6rem;
  width: 100%;
  padding: 1.5rem;

  box-shadow: ${({ $isEmpty }) =>
    $isEmpty
      ? 'none'
      : `
    0px 2px 4px 0px rgba(18, 18, 23, 0.04),
    0px 5px 8px 0px rgba(18, 18, 23, 0.04),
    0px 10px 18px 0px rgba(18, 18, 23, 0.03),
    0px 24px 48px 0px rgba(18, 18, 23, 0.03),
    0px 0px 0px 1px rgba(255, 255, 255, 0.24)`};
  border-radius: 0.8rem;
  background-color: ${({ $isEmpty }) =>
    $isEmpty ? 'rgba(255, 255, 255, 0.6)' : '#fff'};
`

const CardHeaderStyled = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  padding: 0 1rem;
`

const CardTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
`

const CardSubtitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.light};
`

const CardListStyled = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);

  gap: 1.25rem;
  width: 100%;
`

const CardItemStyled = styled.div<{
  $hasOverlay?: boolean
  $isDeleting?: boolean
}>`
  display: flex;
  flex-direction: column;

  gap: 0.8rem;
  padding: 0.8rem 1rem 1rem;

  border: 1px solid
    ${({ $hasOverlay, $isDeleting }) =>
      $isDeleting ? '#ff3b30' : $hasOverlay ? '#D7DFE9' : '#ff9500'};
  border-radius: 0.6rem;

  position: relative;

  &:before {
    content: ${({ $hasOverlay }) => ($hasOverlay ? '""' : '')};
    width: 100%;
    height: 100%;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 0.6rem;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 1;
  }
`

const DateStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 1rem;
  text-align: left;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const WarningIconStyled = styled(WarningIcon)`
  width: 0.8rem;
  height: 0.8rem;
  fill: #ff9500;
`

const InfoBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.4rem;
`

const AvatarInitialsStyled = styled(AvatarInitials)`
  width: 1.6rem;
  height: 1.6rem;
`

const InfoBlockTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

const RoleBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
`

const RoleBlockLabelStyled = styled.p`
  color: ${theme.colorsNew.midGrey200};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
`

const CustomSelectStyled = styled(CustomSelect)<{ $noValue?: boolean }>`
  .Select__control {
    height: 1.8rem;
    min-height: 1.8rem;
    border-color: ${({ $noValue }) => ($noValue ? '#ff9500' : null)};
    :hover {
      border-color: ${({ $noValue }) => ($noValue ? '#ff9500' : null)};
    }
  }
`

const ShiftBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`

const RowCellStyled = styled.div<{ $isShortShift?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.1rem 0;
  position: relative;

  color: ${({ $isShortShift }) =>
    $isShortShift ? '#E18700' : theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  text-align: center;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  :first-of-type {
    justify-content: flex-start;
  }
`
const RowStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  grid-template-columns: 1.2fr 1fr 1fr;
`

const CustomTimePickerStyled = styled(CustomTimePicker)<{
  $hasConflict?: boolean
}>`
  height: unset;
  padding: calc(0.1rem - 0.75px) 0.4rem;

  border: 1px solid
    ${({ $hasConflict }) =>
      $hasConflict ? '#FFA000' : 'rgba(164, 170, 185, 0.4)'};
  background: unset;
  border-radius: 0.8rem;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  :hover:not(:disabled),
  :focus:not(:disabled) {
    border-color: ${({ $hasConflict }) => ($hasConflict ? '#FFA000' : null)};
  }
  ::placeholder {
    color: ${({ $hasConflict }) => ($hasConflict ? '#FFA000' : null)};
  }
`

const CardFooterStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  gap: 0.4rem;
  margin-top: 0.5rem;
`

const ButtonWrapStyled = styled.div`
  display: flex;
  flex: 1;
  & + & {
    justify-content: flex-end;
  }
`

const FooterButtonStyled = styled(OutlineButton)`
  gap: 0.4rem;
  width: unset;
  height: 1.8rem;
  padding: 0.1rem 0.6rem;

  border-width: 1px;
  border-radius: 1.2rem;

  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const DeleteButtonStyled = styled(FooterButtonStyled)`
  border-color: transparent;
  background-color: rgba(69, 84, 104, 0.05);
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.8rem;
`

const DeleteIconStyled = styled(DeleteIcon)`
  width: 0.7rem;
  height: 0.7rem;
  fill: currentColor;
`

const OrangeButtonStyled = styled(FooterButtonStyled)<{
  $isActive?: boolean
}>`
  flex: 1;
  position: relative;

  border: 0;
  background: linear-gradient(#ffa100, #ff4d00); // initial gradient

  color: #fff;
  overflow: hidden;
  pointer-events: ${({ $isActive }) => ($isActive ? 'none' : null)};
  z-index: 2;

  p:first-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 0 : 1)};
    transition: opacity 0.3s ease-in-out;
    z-index: 4;
  }

  p:last-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 1 : 0)};
    transition: opacity 1s ease-in-out;
    z-index: 4;
  }
  ::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      90deg,
      #01d9d5 0%,
      #00befa 18.27%,
      #9967ff 42.79%,
      #fd5c61 61.54%,
      #ff902d 84.13%,
      #ffa000 97.12%
    );

    opacity: 0;
    transition: opacity 1s ease-in;
    z-index: 3;
  }
  ::after {
    opacity: ${({ $isActive }) => ($isActive ? 1 : null)};
  }
`

const GreyButtonStyled = styled(FooterButtonStyled)`
  border-color: #848da3;
  background-color: #fff;
  color: #848da3;
  z-index: 2;
`

const GreenButtonStyled = styled(FooterButtonStyled)<{
  $isActive?: boolean
}>`
  flex: ${({ $isActive }) => ($isActive ? '0' : '1')};
  position: relative;

  box-shadow: ${({ $isActive }) => ($isActive ? 'none' : null)};
  border-color: ${({ $isActive }) => ($isActive ? '#AFBACA' : null)};
  background-color: #fff;
  color: ${({ $isActive }) => ($isActive ? '#848DA3' : null)};
  z-index: 2;
  transition:
    flex 1s,
    background-color 0.3s ease-in-out,
    border-color 1s ease-in-out,
    color 0.3s ease-in-out;
  span:first-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 0 : 1)};
    transition: opacity 0.5s ease-in-out;
  }
  span:last-of-type {
    opacity: ${({ $isActive }) => ($isActive ? 1 : 0)};
    transition: opacity 0.5s ease-in-out;
  }

  :hover,
  :focus {
    box-shadow: ${({ $isActive }) => ($isActive ? 'none' : null)};
    border-color: ${({ $isActive }) => ($isActive ? '#AFBACA' : '#4BCCAD')};
    background-color: ${({ $isActive }) => ($isActive ? '#fff' : '#4BCCAD')};
    color: ${({ $isActive }) => ($isActive ? '#848DA3' : '#fff')};
  }
`

const CardEmptyBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.3rem;
  opacity: 0.8;

  color: #32ade6;
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  img {
    width: 1rem;
    height: 1rem;
  }
`

const TooltipStyled = styled(Popover)`
  width: unset;
  min-width: 6rem;
  max-width: 12rem;
  padding: 0.5rem 1rem;
  margin-bottom: 0.5rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: #000;

  color: #fff;
  font-size: 0.8rem;
  font-family: ${theme.fonts.regular};
  line-height: normal;

  z-index: 1051;
  .arrow {
    :before,
    :after {
      border-bottom-color: #000;
    }
  }
`
