import React, { forwardRef, useEffect, useState } from 'react'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import Popover from 'react-bootstrap/Popover'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { CompanyAvatar } from 'components/ui/AvatarInitials'

import { database } from '../../../../index'

import { Company } from 'types/company'
import { IEmployee } from 'types/employee'

import arrowRightIcon from 'img/icons/arrowRightThicIcon.svg'
import closeIcon from 'img/icons/closeIcon.svg'
import categoriesIcon from 'img/icons/folderOpenIcon.svg'
import clockIcon from 'img/icons/hoursIcon.svg'
import plusIcon from 'img/icons/plusIcon.svg'
import trashIcon from 'img/icons/trashNewIcon.svg'
import sortIcon from 'img/icons/upDownIcon.svg'
import roleIcon from 'img/icons/userNewIcon.svg'

dayjs.extend(relativeTime)

// Activity log types
type ActivityLogEntry = {
  id: string
  timestamp: number
  authorId: string
  authorName: string
  authorAvatar?: string
  action:
    | 'start-end'
    | 'role'
    | 'start'
    | 'end'
    | 'delete-shift'
    | 'add-shift'
    | 'break'
    | 'unplanned'
    | 'add-break'
    | 'delete-break'
  oldValue?: string | number
  newValue?: string | number
  status: 'modified' | 'added' | 'deleted'
}

type ActivitiesPopoverProps = {
  onClose: () => void
  employee?: IEmployee
  date?: string
  currentCompany?: Company
}

const ActivitiesPopover = forwardRef<HTMLDivElement, ActivitiesPopoverProps>(
  ({ onClose, employee, date, currentCompany, ...rest }, ref) => {
    const [activities, setActivities] = useState<ActivityLogEntry[]>([])
    const [loading, setLoading] = useState(true)
    const [sortBy, setSortBy] = useState<'timestamp' | 'action'>('timestamp')
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
    const [filterCategory, setFilterCategory] = useState<string>('all')

    // Load activity log from Firebase
    useEffect(() => {
      if (!employee?.uid || !date || !currentCompany?.key) {
        setLoading(false)
        return
      }

      const activityRef = database
        .ref(
          `AttendanceActivityLog/${currentCompany.key}/${date}/${employee.uid}`
        )
        .orderByChild('timestamp')

      const unsubscribe = activityRef.on('value', snapshot => {
        const data = snapshot.val() || {}
        const activityList: ActivityLogEntry[] = Object.entries(data).map(
          ([id, activity]) => ({
            id,
            ...(activity as Omit<ActivityLogEntry, 'id'>)
          })
        )

        setActivities(activityList.reverse()) // Most recent first
        setLoading(false)
      })

      return () => {
        activityRef.off('value', unsubscribe)
      }
    }, [employee?.uid, date, currentCompany?.key])

    // Sort and filter activities
    const filteredAndSortedActivities = React.useMemo(() => {
      let filtered = activities

      if (filterCategory !== 'all') {
        filtered = activities.filter(
          activity => activity.action === filterCategory
        )
      }

      return filtered.sort((a, b) => {
        if (sortBy === 'timestamp') {
          return sortOrder === 'desc'
            ? b.timestamp - a.timestamp
            : a.timestamp - b.timestamp
        } else {
          const aValue = a.action
          const bValue = b.action
          return sortOrder === 'desc'
            ? bValue.localeCompare(aValue)
            : aValue.localeCompare(bValue)
        }
      })
    }, [activities, sortBy, sortOrder, filterCategory])

    // Helper function to format time values
    const formatTimeValue = (value: string | number | undefined): string => {
      if (value === undefined || value === null) return 'N/A'
      if (typeof value === 'number') {
        const hours = Math.floor(value / 60)
        const minutes = value % 60
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
      }
      return String(value)
    }

    // Helper function to get relative time
    const getRelativeTime = (timestamp: number): string => {
      return dayjs(timestamp).fromNow()
    }

    // Helper function to get category icon
    const getCategoryIcon = (action: string): string => {
      switch (action) {
        case 'start-end':
        case 'start':
        case 'end':
        case 'break':
        case 'unplanned':
          return clockIcon
        case 'role':
          return roleIcon
        case 'add-shift':
        case 'add-break':
          return plusIcon
        case 'delete-shift':
        case 'delete-break':
          return trashIcon
        default:
          return clockIcon
      }
    }

    const categories = [
      {
        value: 'all',
        label: I18n.t('common.all', { defaultValue: 'All' }),
        icon: categoriesIcon
      },
      {
        value: 'start-end',
        label: I18n.t('payroll.start_end'),
        icon: clockIcon
      },
      {
        value: 'role',
        label: I18n.t('payroll.role'),
        icon: roleIcon
      },
      {
        value: 'start',
        label: I18n.t('payroll.start'),
        icon: clockIcon
      },
      {
        value: 'end',
        label: I18n.t('payroll.end'),
        icon: clockIcon
      },
      {
        value: 'delete-shift',
        label: I18n.t('payroll.delete_shift'),
        icon: trashIcon
      },
      {
        value: 'add-shift',
        label: I18n.t('payroll.add_shift'),
        icon: plusIcon
      },
      {
        value: 'break',
        label: I18n.t('payroll.break'),
        icon: clockIcon
      },
      {
        value: 'unplanned',
        label: I18n.t('payroll.unplanned'),
        icon: clockIcon
      },
      {
        value: 'add-break',
        label: I18n.t('payroll.add_break'),
        icon: plusIcon
      },
      {
        value: 'delete-break',
        label: I18n.t('payroll.delete_break'),
        icon: trashIcon
      }
    ]

    const statuses = [
      {
        value: 'modified',
        label: I18n.t('payroll.modified')
      },
      {
        value: 'added',
        label: I18n.t('payroll.added')
      },
      {
        value: 'deleted',
        label: I18n.t('payroll.deleted')
      }
    ]
    return (
      <PopoverStyled
        {...rest}
        id='payroll_activities-popover'
        ref={ref}
      >
        <ContainerStyled>
          <HeaderWrapStyled>
            <HeaderStyled>
              <HeaderTitleStyled>
                {I18n.t('payroll.activity_log')}
                <span>{filteredAndSortedActivities.length}</span>
              </HeaderTitleStyled>
              <HeaderButtonStyled
                onClick={() =>
                  setFilterCategory(
                    filterCategory === 'all' ? 'start-end' : 'all'
                  )
                }
                style={{ opacity: filterCategory !== 'all' ? 1 : 0.35 }}
              >
                <img
                  src={categoriesIcon}
                  alt=''
                />
                {filterCategory === 'all'
                  ? I18n.t('payroll.categories')
                  : categories.find(c => c.value === filterCategory)?.label}
              </HeaderButtonStyled>
              <HeaderButtonStyled
                onClick={() => {
                  if (sortBy === 'action') {
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
                  } else {
                    setSortBy('action')
                    setSortOrder('asc')
                  }
                }}
                style={{ opacity: sortBy === 'action' ? 1 : 0.35 }}
              >
                <img
                  src={sortIcon}
                  alt=''
                />
                {I18n.t('payroll.action', { defaultValue: 'Action' })}
              </HeaderButtonStyled>
              <div />
              <HeaderButtonStyled
                onClick={() => {
                  if (sortBy === 'timestamp') {
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
                  } else {
                    setSortBy('timestamp')
                    setSortOrder('desc')
                  }
                }}
                style={{ opacity: sortBy === 'timestamp' ? 1 : 0.35 }}
              >
                <img
                  src={sortIcon}
                  alt=''
                />
                {I18n.t('payroll.time', { defaultValue: 'Time' })}
              </HeaderButtonStyled>
              <div />
              <CloseButtonStyled onClick={onClose}>
                <img
                  src={closeIcon}
                  alt='Close'
                />
              </CloseButtonStyled>
            </HeaderStyled>
          </HeaderWrapStyled>

          <ListStyled>
            {loading ? (
              <LoadingStyled>
                {I18n.t('common.loading', { defaultValue: 'Loading...' })}
              </LoadingStyled>
            ) : filteredAndSortedActivities.length === 0 ? (
              <EmptyListStyled>
                {I18n.t('payroll.no_activity_to_date', {
                  defaultValue: 'No activity recorded for this shift'
                })}
              </EmptyListStyled>
            ) : (
              filteredAndSortedActivities.map(activity => (
                <ListItemStyled key={activity.id}>
                  <TimeAvatarBlockStyled>
                    <OverlayTrigger
                      trigger={['hover', 'focus']}
                      placement='top'
                      overlay={
                        <TooltipStyled
                          id={`payroll_activities-popover-time-${activity.id}`}
                        >
                          {dayjs(activity.timestamp).format(
                            'MMMM D, YYYY [at] h:mm A'
                          )}
                        </TooltipStyled>
                      }
                    >
                      <TimeBlockStyled>
                        <img
                          src={clockIcon}
                          alt=''
                        />
                        {getRelativeTime(activity.timestamp)}
                      </TimeBlockStyled>
                    </OverlayTrigger>

                    <OverlayTrigger
                      trigger={['hover', 'focus']}
                      placement='top'
                      overlay={
                        <TooltipStyled
                          id={`payroll_activities-popover-avatar-${activity.id}`}
                        >
                          {activity.authorName}
                        </TooltipStyled>
                      }
                    >
                      <AvatarWrapStyled>
                        <CompanyAvatarStyled
                          avatar={activity.authorAvatar}
                          initials={activity.authorName
                            .split(' ')
                            .map(n => n[0])
                            .join('')
                            .toUpperCase()}
                        />
                      </AvatarWrapStyled>
                    </OverlayTrigger>
                  </TimeAvatarBlockStyled>

                  <CategoryStyled>
                    <img
                      src={getCategoryIcon(activity.action)}
                      alt=''
                    />
                    {categories.find(c => c.value === activity.action)?.label ||
                      activity.action}
                  </CategoryStyled>

                  <ChangesTextStyled>
                    {formatTimeValue(activity.oldValue)}
                  </ChangesTextStyled>

                  <ArrowRightStyled
                    src={arrowRightIcon}
                    alt=''
                  />

                  <ChangesTextStyled>
                    {formatTimeValue(activity.newValue)}
                  </ChangesTextStyled>

                  <StatusStyled $status={activity.status}>
                    {statuses.find(s => s.value === activity.status)?.label ||
                      activity.status}
                  </StatusStyled>
                </ListItemStyled>
              ))
            )}
          </ListStyled>
        </ContainerStyled>
      </PopoverStyled>
    )
  }
)

export default ActivitiesPopover

const PopoverStyled = styled(Popover)`
  width: 40rem;
  max-width: 40rem;
  padding: 0;

  border: 0;
  border-radius: 0.8rem;

  .arrow {
    display: none;
  }
`

const ContainerStyled = styled.div`
  display: flex;
  flex-direction: column;
`

const HeaderWrapStyled = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
`

const HeaderStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  grid-template-columns: 1fr 0.8fr 1fr 1.4rem 1fr 0.6fr;
`

const HeaderTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  span {
    margin-left: 0.5rem;
    padding: 0.2rem 0.5rem;
    border-radius: 0.8rem;
    background-color: rgba(28, 34, 43, 0.05);

    font-size: 0.8rem;
    font-family: ${theme.fonts.normal};
  }
`

const HeaderButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: flex-start;

  gap: 0.5rem;
  padding: 0;

  border: 0;
  opacity: 0.35;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};

  img {
    width: 0.8rem;
    height: 0.8rem;
  }

  :hover,
  :focus {
    opacity: 1;
  }

  :not(:first-of-type),
  :not(:nth-of-type(1)) {
    justify-content: center;
  }
`

const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;
  position: absolute;
  right: 1.5rem;
  top: 1.5rem;

  border: none;
  opacity: 0.5;
  background: none;
  img {
    width: 0.9rem;
    height: 0.9rem;
  }
  :hover,
  :focus {
    opacity: 1;
  }
`

const ListStyled = styled.div`
  display: flex;
  flex-direction: column;

  padding: 0 1.5rem 1.5rem;
  max-height: 27rem;

  overflow-y: auto;
`

const ListItemStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  padding: 0.8rem 0;
  grid-template-columns: 1fr 0.9fr 1fr 1.4rem 1fr 0.6fr;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
`

const TimeAvatarBlockStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.8rem;
`

const TimeBlockStyled = styled.button`
  display: flex;
  align-items: center;

  gap: 0.3rem;
  padding: 0;
  min-width: 3.5rem;

  border: 0;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.light};
  line-height: normal;

  img {
    width: 1.1rem;
    height: 1.1rem;
  }
`

const AvatarWrapStyled = styled.button`
  width: 2rem;
  height: 2rem;
  padding: 0;
  border: 0;
  background-color: transparent;
`

const CompanyAvatarStyled = styled(CompanyAvatar)`
  width: 2rem;
  height: 2rem;
  font-size: 0.85rem;
`

const CategoryStyled = styled.div`
  display: flex;
  align-items: center;
  justify-self: flex-start;

  gap: 0.3rem;
  padding: 0.2rem 0.6rem 0.2rem 0.4rem;

  border: 1px solid rgba(10, 12, 17, 0.1);
  border-radius: 0.8rem;
  background-color: rgba(28, 34, 43, 0.02);

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.775rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  img {
    width: 0.85rem;
    height: 0.85rem;
  }
`

const ChangesTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.875rem;
  text-align: center;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const ArrowRightStyled = styled.img`
  width: 0.8rem;
  height: 0.8rem;
  margin: auto;
`

const StatusStyled = styled.div<{ $status?: string }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.3rem;

  border-radius: 0.8rem;
  background-color: ${props => {
    switch (props.$status) {
      case 'added':
        return 'rgba(34, 197, 94, 0.1)'
      case 'deleted':
        return 'rgba(239, 68, 68, 0.1)'
      case 'modified':
        return 'rgba(59, 130, 246, 0.1)'
      default:
        return 'rgba(28, 34, 43, 0.05)'
    }
  }};

  color: ${props => {
    switch (props.$status) {
      case 'added':
        return '#16a34a'
      case 'deleted':
        return '#dc2626'
      case 'modified':
        return '#2563eb'
      default:
        return theme.colorsNew.darkGrey500
    }
  }};
  font-size: 0.8rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

const LoadingStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

const TooltipStyled = styled(Popover)`
  min-width: 6rem;
  width: unset;
  padding: 0.5rem 1rem;
  max-width: 12rem;
  margin-bottom: 0.5rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: #242a38;

  color: #fff;
  font-size: 0.8rem;
  font-family: ${theme.fonts.regular};
  line-height: normal;

  .arrow {
    :before,
    :after {
      border-top-color: #242a38;
    }
  }
  span {
    font-family: ${theme.fonts.bold};
  }
`

const EmptyListStyled = styled.p`
  margin-top: 1rem;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  text-align: center;
  font-family: ${theme.fonts.light};
`
