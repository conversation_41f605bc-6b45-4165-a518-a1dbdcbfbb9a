import React, { forwardRef, useState, useEffect } from 'react'
import Popover from 'react-bootstrap/Popover'
import { DayPicker } from 'react-day-picker'
import { enUS } from 'react-day-picker/locale'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import dayjs, { Dayjs } from 'dayjs'
import moment from 'moment'
import { RootState } from 'store/reducers'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import CustomSelect, { OptionType } from 'components/ui/Select'

import { AttendanceSettings } from 'types/attendance'
import { ALL_DAYS } from 'utils/constants'

import { ReactComponent as CheckIcon } from 'img/icons/checkGreenFat.svg'
import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'

type Props = {
  onClose: () => void
  startOfPeriod: Dayjs
  payrollLength: number
  currentPeriodOffset: number
  onPeriodSelect: (offset: number) => void
  attendanceSettings: AttendanceSettings
  payrollStartingDay?: string
  activeWeekPeriodTab?: string
}

const CalendarPopover = forwardRef<HTMLDivElement, Props>(
  (
    {
      onClose,
      startOfPeriod,
      payrollLength,
      currentPeriodOffset,
      onPeriodSelect,
      attendanceSettings,
      payrollStartingDay = 'Monday',
      activeWeekPeriodTab = 'biweekly',
      ...props
    },
    ref,
    ...rest
  ) => {
    // Local state to track the selected period within the modal
    // This doesn't update the context until "Open selected" is clicked
    const [localSelectedOffset, setLocalSelectedOffset] = React.useState(currentPeriodOffset)

    // Reset local selection to match context when modal opens/context changes
    React.useEffect(() => {
      setLocalSelectedOffset(currentPeriodOffset)
    }, [currentPeriodOffset])
    // Calculate effective period based on active week tab
    const getEffectivePeriod = React.useCallback(() => {
      const basePeriodStart = startOfPeriod
        .clone()
        .add(currentPeriodOffset * payrollLength, 'days')

      if (activeWeekPeriodTab === 'week1') {
        // Week 1: First 7 days of the bi-weekly period
        return {
          start: basePeriodStart,
          length: 7
        }
      } else if (activeWeekPeriodTab === 'week2') {
        // Week 2: Second 7 days of the bi-weekly period
        return {
          start: basePeriodStart.clone().add(7, 'days'),
          length: 7
        }
      } else {
        // Biweekly: Full 14-day period
        return {
          start: basePeriodStart,
          length: payrollLength
        }
      }
    }, [startOfPeriod, currentPeriodOffset, payrollLength, activeWeekPeriodTab])

    const effectivePeriod = getEffectivePeriod()
    const currentPeriodStart = effectivePeriod.start
    const effectivePayrollLength = effectivePeriod.length

    // Initialize month/year dropdowns to match the current selected period
    const [selectedYear, setSelectedYear] = useState(currentPeriodStart.format('YYYY'))
    const [selectedMonth, setSelectedMonth] = useState(currentPeriodStart.format('M'))

    // // Flag to prevent unwanted date selections during programmatic updates
    const [isUpdatingProgrammatically, setIsUpdatingProgrammatically] = useState(false)
    const currentPeriodEnd = currentPeriodStart
      .clone()
      .add(effectivePayrollLength - 1, 'days')

    const [selectedRange, setSelectedRange] = useState<{
      from: Date | undefined
      to?: Date | undefined
    }>({
      from: currentPeriodStart.toDate(),
      to: currentPeriodEnd.toDate()
    })

    const isLocaleFr =
      useSelector((state: RootState) => state.i18n.locale) === 'fr'

    // Calculate week starting day for calendar (0 = Sunday, 1 = Monday, etc.)
    const weekStartsOn = React.useMemo(() => {
      const dayIndex = ALL_DAYS[payrollStartingDay] || 1 // Default to Monday
      // ALL_DAYS already uses 0-6 where Sunday=0, Monday=1, etc. - perfect for DayPicker
      return dayIndex as 0 | 1 | 2 | 3 | 4 | 5 | 6
    }, [payrollStartingDay])

    // Update selected range when currentPeriodOffset changes from parent
    useEffect(() => {
      setIsUpdatingProgrammatically(true)

      const newPeriodStart = startOfPeriod
        .clone()
        .add(currentPeriodOffset * payrollLength, 'days')
      const newPeriodEnd = newPeriodStart.clone().add(payrollLength - 1, 'days')

      setSelectedRange({
        from: newPeriodStart.toDate(),
        to: newPeriodEnd.toDate()
      })



      // Reset flag after a brief delay
      setTimeout(() => setIsUpdatingProgrammatically(false), 100)
    }, [startOfPeriod, currentPeriodOffset, payrollLength])

    // Initialize month/year dropdowns only once when component mounts
    const [isInitialized, setIsInitialized] = React.useState(false)
    React.useEffect(() => {
      if (!isInitialized && selectedRange.from) {
        const rangeDate = dayjs(selectedRange.from)
        setSelectedYear(rangeDate.format('YYYY'))
        setSelectedMonth(rangeDate.format('M'))
        setIsInitialized(true)
      }
    }, [selectedRange.from, isInitialized]) // Only initialize once

    // Helper function to find the offset for a given date
    const getOffsetForDate = React.useCallback((date: dayjs.Dayjs) => {
      // Calculate how many days difference between the target date and base period start
      const daysDiff = date.diff(startOfPeriod, 'days')

      // Calculate which period this date should fall into
      // Each period is payrollLength days, so divide by payrollLength and floor
      const estimatedOffset = Math.floor(daysDiff / payrollLength)

      // Check a range around the estimated offset to account for period boundaries
      for (let offset = estimatedOffset - 2; offset <= estimatedOffset + 2; offset++) {
        const periodStart = startOfPeriod.clone().add(offset * payrollLength, 'days')
        const periodEnd = periodStart.clone().add(payrollLength - 1, 'days')
        if (date.isBetween(periodStart, periodEnd, 'day', '[]')) {
          return offset
        }
      }

      // Fallback: broader search if estimation failed
      for (let offset = -50; offset <= 50; offset++) {
        const periodStart = startOfPeriod.clone().add(offset * payrollLength, 'days')
        const periodEnd = periodStart.clone().add(payrollLength - 1, 'days')
        if (date.isBetween(periodStart, periodEnd, 'day', '[]')) {
          return offset
        }
      }

      return 0
    }, [startOfPeriod, payrollLength])

    // Generate period list: center around the locally selected period
    const periodList = React.useMemo(() => {
      const periods = []
      // Center the list around the local selection (5 before, current, 5 after)
      for (let offset = localSelectedOffset - 5; offset <= localSelectedOffset + 5; offset++) {
        const basePeriodStart = startOfPeriod
          .clone()
          .add(offset * payrollLength, 'days')

        // Calculate period start and end based on active week tab
        let periodStart, periodEnd
        if (activeWeekPeriodTab === 'week1') {
          // Week 1: First 7 days of the bi-weekly period
          periodStart = basePeriodStart
          periodEnd = basePeriodStart.clone().add(6, 'days')
        } else if (activeWeekPeriodTab === 'week2') {
          // Week 2: Second 7 days of the bi-weekly period
          periodStart = basePeriodStart.clone().add(7, 'days')
          periodEnd = basePeriodStart.clone().add(13, 'days')
        } else {
          // Biweekly: Full 14-day period
          periodStart = basePeriodStart
          periodEnd = basePeriodStart.clone().add(payrollLength - 1, 'days')
        }

        // Use local selected offset instead of context offset for selection state
        const isSelected = offset === localSelectedOffset
        periods.push({
          offset,
          start: periodStart,
          end: periodEnd,
          label: `${periodStart.format(isLocaleFr ? 'DD' : 'MMM DD')} - ${periodEnd.format(isLocaleFr ? 'DD MMM' : 'MMM DD')}`,
          isCurrentPeriod: offset === 0,
          isSelected: isSelected
        })
      }

      return periods
    }, [startOfPeriod, payrollLength, localSelectedOffset, isLocaleFr, activeWeekPeriodTab])

    const years = Array.from({ length: 6 }, (_, i) => 2022 + i).map(c =>
      moment().year(c).format('YYYY')
    )

    const yearOptions = years.map(c => ({
      value: c,
      label: c
    }))

    const monthsOptions = Array.from({ length: 12 }, (_, i) => i).map(c => ({
      value: `${c + 1}`,
      label: moment().month(c).format('MMMM')
    }))

    // const handleDateSelect = (date: Date | undefined) => {
    //   // Ignore programmatic updates
    //   if (isUpdatingProgrammatically) {
    //     return
    //   }

    //   if (!date) {
    //     setSelectedRange({ from: undefined, to: undefined })
    //     return
    //   }

    //   // Find which payroll period this date falls into
    //   const selectedDate = dayjs(date)

    //   // Calculate the offset from the current period
    //   let bestOffset = 0
    //   let minDistance = Infinity

    //   // Check periods from -10 to +10 to find the closest match
    //   for (let offset = -10; offset <= 10; offset++) {
    //     const periodStart = startOfPeriod
    //       .clone()
    //       .add(offset * payrollLength, 'days')
    //     const periodEnd = periodStart.clone().add(payrollLength - 1, 'days')

    //     if (selectedDate.isBetween(periodStart, periodEnd, 'day', '[]')) {
    //       bestOffset = offset
    //       break
    //     }

    //     const distance = Math.min(
    //       Math.abs(selectedDate.diff(periodStart, 'days')),
    //       Math.abs(selectedDate.diff(periodEnd, 'days'))
    //     )
    //     if (distance < minDistance) {
    //       minDistance = distance
    //       bestOffset = offset
    //     }
    //   }

    //   // Set the selected range to the found period
    //   const periodStart = startOfPeriod
    //     .clone()
    //     .add(bestOffset * payrollLength, 'days')
    //   const periodEnd = periodStart.clone().add(payrollLength - 1, 'days')

    //   setSelectedRange({
    //     from: periodStart.toDate(),
    //     to: periodEnd.toDate()
    //   })

    //   // Update month/year dropdowns to match the selected period
    //   setIsUpdatingProgrammatically(true)
    //   setSelectedYear(periodStart.format('YYYY'))
    //   setSelectedMonth(periodStart.format('M'))

    //   // Update the period selection
    //   onPeriodSelect(bestOffset)

    //   // Reset the flag after a brief delay
    //   setTimeout(() => setIsUpdatingProgrammatically(false), 100)

    //   // Don't close the popover immediately - let user see the updated period list
    //   // onClose() - removed to keep popover open
    // }
    const handleResetToCurrent = () => {
      const now = new Date()
      const dayOfWeek = dayjs(now).day()
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1

      const startOfCurrentWeek = dayjs(now)
        .subtract(daysToSubtract, 'day')
        .toDate()
      const endOfCurrentWeek = dayjs(startOfCurrentWeek).add(6, 'day').toDate()

      setSelectedRange({ from: startOfCurrentWeek, to: endOfCurrentWeek })
      setSelectedYear(moment().format('YYYY'))
      setSelectedMonth(moment().format('M'))

      // Reset local selection to current period (offset 0)
      setLocalSelectedOffset(0)
    }

    // Calculate effective period based on local selection for date picker highlighting
    const localEffectivePeriod = React.useMemo(() => {
      const basePeriodStart = startOfPeriod
        .clone()
        .add(localSelectedOffset * payrollLength, 'days')

      if (activeWeekPeriodTab === 'week1') {
        return {
          start: basePeriodStart,
          length: 7
        }
      } else if (activeWeekPeriodTab === 'week2') {
        return {
          start: basePeriodStart.clone().add(7, 'days'),
          length: 7
        }
      } else {
        return {
          start: basePeriodStart,
          length: payrollLength
        }
      }
    }, [startOfPeriod, localSelectedOffset, payrollLength, activeWeekPeriodTab])

//    // update the month/year dropdowns when the local selection changes:
useEffect(() => {
  // For month/year dropdowns, use the locally selected period
  // This ensures dropdowns sync with local selection changes
  const basePeriodStart = startOfPeriod
    .clone()
    .add(localSelectedOffset * payrollLength, 'days')

  setSelectedYear(basePeriodStart.format('YYYY'))
  setSelectedMonth(basePeriodStart.format('M'))

  // For the date picker highlighting, use the calculated effective period
  const newPeriodStart = localEffectivePeriod.start
  const newPeriodEnd = newPeriodStart.clone().add(localEffectivePeriod.length - 1, 'days')

  setSelectedRange({
    from: newPeriodStart.toDate(),
    to: newPeriodEnd.toDate()
  })
}, [startOfPeriod, localSelectedOffset, payrollLength, localEffectivePeriod])
    return (
      <PopoverStyled
        ref={ref}
        {...props}
        {...rest}
      >
        <ContainerStyled>
          <CloseButtonStyled onClick={onClose}>
            <CloseIconStyled />
          </CloseButtonStyled>
          <MainStyled>
            <CalendarBlockStyled>
              <SelectBlockStyled>
                <CustomSelectStyled
                  options={monthsOptions}
                  value={monthsOptions.find(
                    ({ value }) => value === selectedMonth
                  )}
                  onChange={(option: OptionType) => {
                    const newMonth = String(option.value)
                    setSelectedMonth(newMonth)

                    // Find a period that starts in the selected month
                    // Try different days in the month to find a period start
                    const targetMonth = parseInt(newMonth)
                    const targetYear = parseInt(selectedYear)
                    let foundOffset = 0

                    // Check the first 28 days of the month to find a period that starts in this month
                    for (let day = 1; day <= 28; day++) {
                      const testDate = dayjs(`${targetYear}-${targetMonth}-${day.toString().padStart(2, '0')}`)
                      const testOffset = getOffsetForDate(testDate)

                      // Check if this period actually starts in the target month
                      const periodStart = startOfPeriod.clone().add(testOffset * payrollLength, 'days')

                      if (periodStart.month() === targetMonth - 1 && periodStart.year() === targetYear) {
                        foundOffset = testOffset
                        break
                      }
                    }

                    // Update local selection only, don't update context yet
                    setLocalSelectedOffset(foundOffset)
                  }}
                  components={{
                    IndicatorSeparator: () => null
                  }}
                />
                <CustomSelectStyled
                  options={yearOptions}
                  value={yearOptions.find(
                    ({ value }) => value === selectedYear
                  )}
                  onChange={(option: OptionType) => {
                    const newYear = String(option.value)
                    setSelectedYear(newYear)

                    // Find a period that starts in the selected month/year
                    const targetMonth = parseInt(selectedMonth)
                    const targetYear = parseInt(newYear)
                    let foundOffset = 0

                    // Check the first 28 days of the month to find a period that starts in this month/year
                    for (let day = 1; day <= 28; day++) {
                      const testDate = dayjs(`${targetYear}-${targetMonth}-${day.toString().padStart(2, '0')}`)
                      const testOffset = getOffsetForDate(testDate)

                      // Check if this period actually starts in the target month/year
                      const periodStart = startOfPeriod.clone().add(testOffset * payrollLength, 'days')

                      if (periodStart.month() === targetMonth - 1 && periodStart.year() === targetYear) {
                        foundOffset = testOffset
                        break
                      }
                    }

                    // Update local selection only, don't update context yet
                    setLocalSelectedOffset(foundOffset)
                  }}
                  components={{
                    IndicatorSeparator: () => null
                  }}
                />
              </SelectBlockStyled>

              <DayPickerStyled
                showOutsideDays
                hideNavigation
                animate
                mode='single'
                selected={selectedRange.from}
                // onSelect={handleDateSelect}
                locale={enUS}
                weekStartsOn={weekStartsOn as 0 | 1 | 2 | 3 | 4 | 5 | 6}
                month={new Date(parseInt(selectedYear), parseInt(selectedMonth) - 1)}
                key={`${selectedYear}-${selectedMonth}`}
                required
                formatters={{
                  formatWeekdayName: (day: Date) =>
                    dayjs(day).format('ddd').slice(0, -1)
                }}
                modifiers={{
                  weekRange: date => {
                    if (!selectedRange.from || !selectedRange.to) return false
                    const d = dayjs(date).startOf('day')
                    const from = dayjs(selectedRange.from).startOf('day')
                    const to = dayjs(selectedRange.to).startOf('day')
                    return (
                      (d.isSame(from) || d.isAfter(from)) &&
                      (d.isSame(to) || d.isBefore(to))
                    )
                  },
                  today: date => {
                    return dayjs(date).isSame(dayjs(), 'day')
                  }
                }}
                modifiersClassNames={{
                  weekRange: 'rdp-day_selected-week',
                  today: 'rdp-day_today'
                }}
              />
            </CalendarBlockStyled>
            <ListBlockStyled>
              <ListLabelStyled>
                {I18n.t('payroll.select_new_period')}
              </ListLabelStyled>
              <ListWrapStyled>
                <ListStyled>
                  {periodList.map((period) => (
                    <ListItemStyled
                      key={period.offset}
                      $isCurrentPeriod={period.isCurrentPeriod}
                      $isSelected={period.isSelected}
                      onClick={() => {
                        // Only update local selection, don't update context yet
                        setLocalSelectedOffset(period.offset)
                        // Don't close immediately - let user see the selection update
                      }}
                      $isLowerCase={isLocaleFr}
                    >
                      {period.label}
                      <CircleStyled>
                        {period.isSelected && <CheckIconStyled />}
                      </CircleStyled>
                    </ListItemStyled>
                  ))}
                </ListStyled>
              </ListWrapStyled>
            </ListBlockStyled>
          </MainStyled>

          <FooterStyled>
            <ResetButtonStyled onClick={handleResetToCurrent}>
              {I18n.t('payroll.reset_to_current')} ↩
            </ResetButtonStyled>
            <OpenButtonStyled
              disabled={false}
              onClick={() => {
                // Apply the local selection to context and close modal
                onPeriodSelect(localSelectedOffset)
                onClose()
              }}
            >
              {I18n.t('payroll.open_selected')}
            </OpenButtonStyled>
          </FooterStyled>
        </ContainerStyled>
      </PopoverStyled>
    )
  }
)

export default CalendarPopover

const PopoverStyled = styled(Popover)`
  width: 32rem;
  max-width: 32rem;

  border: 0;
  box-shadow:
    0px 2px 4px -1px rgba(18, 18, 23, 0.06),
    0px 4px 6px -1px rgba(18, 18, 23, 0.08);
  border-radius: 0.8rem;
  margin-left: 5.5rem !important;

  .arrow {
    display: none;
  }
`

const ContainerStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 1rem;
  padding: 1rem;
  position: relative;
`

const CloseButtonStyled = styled.button`
  display: flex;
  padding: 0.2rem;

  position: absolute;
  right: 0.5rem;
  top: 0.5rem;

  border: none;
  opacity: 0.5;
  background: none;
  :hover,
  :focus {
    opacity: 1;
  }
`

const CloseIconStyled = styled(CloseIcon)`
  width: 0.8rem;
  height: 0.8rem;

  fill: ${theme.colors.darkGrey};
`

const MainStyled = styled.div`
  display: flex;
  gap: 1rem;
`

const CalendarBlockStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.5rem;
`

const SelectBlockStyled = styled.div`
  display: flex;

  gap: 0.5rem;
  width: 100%;
`

const CustomSelectStyled = styled(CustomSelect)``

const DayPickerStyled = styled(DayPicker)`
  height: calc(2.2rem * 7 + 2px + 0.6rem);
  padding: 0.4rem 0.4rem 0.2rem 0.4rem;

  border: 1px solid #d7dfe9;
  border-radius: 0.8rem;

  --rdp-day_button-height: 2.2rem;
  --rdp-day_button-width: 2.2rem;
  --rdp-day-width: 2.2rem;
  --rdp-day-height: 2.2rem;
  --rdp-outside-opacity: 0.3;

  .rdp-month_caption {
    display: none;
  }
  .rdp-weekday {
    height: 2.2rem;
    color: #455468;
    font-size: 0.825rem;
    font-family: ${theme.fonts.bold};
    opacity: 1;
  }
  .rdp-day_button {
    color: #455468;
    font-size: 0.825rem;
    font-family: ${theme.fonts.normal};
  }

  .rdp-day_selected-week {
    background-color: rgba(50, 173, 230, 0.2);
    opacity: 1;
    &.rdp-selected .rdp-day_button {
      border: 0;
    }
  }

  .rdp-day_today .rdp-day_button {
    background-color: #3BBCFF;
    color: white;
    font-weight: bold;
    border-radius: 50%;
  }

  .rdp-today {
    .rdp-day_button {
      color: #fff;
      background-color: #32ade6;
    }
  }
  .rdp-week {
    .rdp-day:first-of-type {
      border-top-left-radius: 50%;
      border-bottom-left-radius: 50%;
    }
    .rdp-day:last-of-type {
      border-top-right-radius: 50%;
      border-bottom-right-radius: 50%;
    }
  }
  .rdp-week:hover {
    background-color: #efeff2;
  }
`

const ListBlockStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.5rem;
  flex: 1;
  position: relative;
`

const ListLabelStyled = styled.div`
  display: flex;
  align-items: center;

  height: 2.25rem;
  padding-left: 0.5rem;

  color: #455468;
  font-size: 0.9rem;
  font-family: ${theme.fonts.bold};
  line-height: normal;
`

const ListStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.4rem;
  padding: 1.2rem 0;
  max-height: calc(2.2rem * 7 + 0.6rem);

  overflow-y: auto;
`

const ListWrapStyled = styled.div`
  position: relative;

  :before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2.4rem;
    background: linear-gradient(
      to top,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1)
    );

    pointer-events: none;
  }
  :after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2.4rem;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1)
    );

    pointer-events: none;
  }
`

const CircleStyled = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.2rem;
  height: 1.2rem;

  border: 1px solid #cccccc;
  border-radius: 50%;
  background-color: rgba(204, 204, 204, 0.2);
`

const CheckIconStyled = styled(CheckIcon)`
  width: 0.75rem;
  height: 0.75rem;
  fill: #32ade6;
`

const ListItemStyled = styled.button<{
  $isCurrentPeriod: boolean
  $isSelected: boolean
  $isLowerCase?: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0.35rem 0.4rem 0.35rem 1rem;

  border: 1.5px solid
    ${({ $isCurrentPeriod, $isSelected }) =>
      $isCurrentPeriod || $isSelected ? '#3BBCFF' : 'transparent'};
  border-radius: 1.2rem;
  background: ${({ $isSelected }) =>
    $isSelected
      ? 'linear-gradient(180deg, #3BBCFF, #2D87FF) !important'
      : 'transparent'};

  color: ${({ $isSelected }) => ($isSelected ? '#fff' : '#455468')};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  text-transform: ${({ $isLowerCase }) => ($isLowerCase ? 'lowercase' : null)};

  ${CircleStyled} {
    border-color: ${({ $isSelected }) =>
      $isSelected ? '#fff !important' : null};
    background-color: ${({ $isSelected }) =>
      $isSelected ? '#fff !important' : null};
  }

  :hover,
  :focus {
    background-color: #efeff2;

    ${CircleStyled} {
      border-color: #afbaca;
      background-color: rgba(69, 84, 104, 0.1);
    }
  }
`

const FooterStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const ResetButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.2rem;
  padding: 0.25rem 1rem;

  border: 1px solid #afbaca;
  border-radius: 0.8rem;
  background-color: #fff;

  color: #848da3;
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  :hover,
  :focus {
    color: #fff;
    border-color: #32ade6;
    background-color: #32ade6;
  }
`

const OpenButtonStyled = styled(ResetButtonStyled)<{ disabled?: boolean }>`
  color: ${({ disabled }) => (disabled ? '#afbaca' : '#32ade6')};
  border-color: ${({ disabled }) => (disabled ? '#afbaca' : '#32ade6')};
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};

  :hover,
  :focus {
    color: ${({ disabled }) => (disabled ? '#afbaca' : '#fff')};
    border-color: ${({ disabled }) => (disabled ? '#afbaca' : '#32ade6')};
    background-color: ${({ disabled }) => (disabled ? '#fff' : '#32ade6')};
  }
`
