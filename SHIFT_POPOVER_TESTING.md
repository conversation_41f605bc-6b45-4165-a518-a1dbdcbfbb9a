# ShiftPopover Conflict Analysis Testing Guide

This guide explains how to test the new ShiftPopover conflict analysis implementation with comprehensive test data.

## 🎯 What Was Implemented

The ShiftPopover component now includes a sophisticated conflict analysis system that handles:

- **🔴 Red Conflicts**: Overlapping shifts that must be resolved immediately
- **🟠 Orange Conflicts**: Issues requiring selection, modification, or approval
- **⚫ Grey Conflicts**: Missing required fields that block saving
- **🟢 Green Status**: Ready to save or approve

## 🧪 Test Data Generation

### Quick Setup

1. **Open your Pivot app** in the browser
2. **Open browser console** (F12 → Console tab)
3. **Run the test data generator**:

```javascript
// Replace 'your-company-id' with your actual test company ID
await window.testData.saveAttendanceData('your-company-id')
```

### Test Scenarios Created

The generator creates 10 comprehensive test scenarios:

#### 🔴 **Red Conflicts (Must Resolve Immediately)**

**Employee 1 - Today**: Overlapping shifts
- Shift 1: 8:00 AM - 12:00 PM (Server)
- Shift 2: 11:00 AM - 3:00 PM (<PERSON>) ← **Overlaps with <PERSON><PERSON> 1**

**Employee 1 - Tomorrow**: Multiple conflicts
- Shift 1: 8:00 AM - 12:00 PM (No role assigned)
- Shift 2: 11:40 AM - 4:00 PM (<PERSON>) ← **Overlaps with Shift 1**
- Shift 3: 5:00 PM - 6:00 PM (Manager) ← **Very short shift**

#### 🟠 **Orange Conflicts (Require Approval/Modification)**

**Employee 2 - Today**: Early arrival
- Shift 1: 7:30 AM - 12:00 PM ← **Started 30 min early** (scheduled 8:00 AM)

**Employee 4 - Today**: Missing clock out
- Shift 1: 8:00 AM - ??? ← **No end time recorded**

**Employee 5 - Today**: Short shift
- Shift 1: 8:00 AM - 10:00 AM ← **Only 2 hours** (less than 3-hour minimum)

**Employee 2 - Tomorrow**: Long shift
- Shift 1: 6:00 AM - 7:00 PM ← **13 hours** (over 12-hour limit)

**Employee 5 - Tomorrow**: Late departure
- Shift 1: 8:00 AM - 12:30 PM ← **Stayed 30 min late** (scheduled until 12:00 PM)

#### ⚫ **Grey Conflicts (Missing Required Fields)**

**Employee 3 - Today**: Missing role
- Shift 1: 8:00 AM - 12:00 PM ← **No position assigned**
- Shift 2: 1:00 PM - 5:00 PM (Cook) ← **Valid shift**

#### 🟢 **Green Status (Perfect Shifts)**

**Employee 3 - Tomorrow**: Properly configured
- Shift 1: 8:00 AM - 12:00 PM (Server)
- Shift 2: 1:00 PM - 5:00 PM (Cook)

## 🔍 Testing the Implementation

### 1. **Navigate to Payroll Hours**
- Go to Payroll → Hours
- Select the test date (today/tomorrow)

### 2. **Test Shift Tab Indicators**
- Look for colored shift tabs with warning icons
- **Red tabs**: Immediate conflicts
- **Orange tabs**: Approval needed
- **Grey tabs**: Missing fields

### 3. **Test Shift Popover**
Click on any employee shift to open the detailed popover:

#### **Visual Elements to Check:**
- **Shift tabs**: Color-coded with conflict indicators
- **Tooltip**: Auto-shows conflict details
- **Button states**: Reflect conflict severity
- **Warning icons**: Show on problematic fields

#### **Button Behavior to Test:**
- **Red conflicts**: No action buttons available
- **Grey conflicts**: Save button disabled
- **Orange conflicts**: Save/Approve with orange styling
- **Green status**: Normal save/approve with green styling

### 4. **Test Specific Scenarios**

#### **Overlapping Shifts (Employee 1)**
- Open Employee 1's shifts for today
- Should see red tabs with warning icons
- Tooltip should explain the overlap
- No save/approve buttons available
- Message: "Resolve conflicts first"

#### **Early Arrival (Employee 2)**
- Open Employee 2's shift for today
- Should see orange tab
- Tooltip explains early start
- Reclaim/Approve buttons available
- Orange button styling

#### **Missing Role (Employee 3)**
- Open Employee 3's first shift for today
- Should see grey tab
- No position selected in dropdown
- Save button disabled
- Message: "Complete required fields"

#### **Perfect Shifts (Employee 3 Tomorrow)**
- Open Employee 3's shifts for tomorrow
- Should see normal blue tabs
- Green save/approve buttons
- No conflict indicators

## 🧹 Cleanup

When done testing, clear the test data:

```javascript
await window.testData.clearAttendanceData('your-company-id')
```

## 🐛 Troubleshooting

### **Test Data Not Appearing**
1. Check console for errors
2. Verify company ID is correct
3. Ensure you have write permissions to the database
4. Refresh the page after generating data

### **Conflicts Not Showing**
1. Check that the analysis utility is imported correctly
2. Verify employee and position IDs exist in your database
3. Check browser console for JavaScript errors

### **Custom Company/Employee IDs**
Edit the test data generator to use your specific IDs:

```javascript
// In attendanceTestDataGenerator.ts
const TEST_EMPLOYEES = [
  'your-employee-id-1',
  'your-employee-id-2',
  // ... etc
]

const TEST_POSITIONS = [
  'your-position-id-1',
  'your-position-id-2',
  // ... etc
]
```

## 📊 Expected Results

After implementing the test data, you should see:

1. **Shift tabs** with appropriate colors and warning icons
2. **Automatic tooltips** explaining conflicts when popovers open
3. **Smart button states** that prevent invalid actions
4. **Real-time conflict detection** as you modify shift data
5. **Contextual messages** guiding users to resolve issues

## 🎉 Success Criteria

The implementation is working correctly if:

- ✅ Red conflicts block all actions until resolved
- ✅ Orange conflicts allow approval with warnings
- ✅ Grey conflicts block saving until fields are completed
- ✅ Green status allows normal save/approve operations
- ✅ Visual indicators clearly communicate conflict types
- ✅ Tooltips provide specific, actionable guidance

## 🔄 Iterative Testing

1. **Generate test data** → **Test scenarios** → **Clear data**
2. **Modify conflict logic** → **Regenerate data** → **Retest**
3. **Add new scenarios** → **Update test generator** → **Validate**

This comprehensive testing approach ensures the conflict analysis system works correctly across all scenarios and provides a solid foundation for production use.
